import { executeSqlSegment } from 'src/api'

interface BlockDataParams {
  connectionId: string
  dataSourceType: string
  operatingObject: string
  databaseName: string
  statement: string
  queryKey: string
}

interface BlockDataResponse {
  resultData: any[]
  detailedResultData: any[]
  executeError?: any
  supportPage?: boolean
  databaseName: string
  connectionId: string
  dataSourceType: string
  operatingObject: string
  statementObject?: string
}

/**
 * 获取块数据或从存储中获取
 * @param startRow 起始行
 * @param sortModel 排序模型
 * @param filterModel 过滤模型
 * @param params 查询参数
 * @returns 数据响应
 */
export async function fetchBlockDataOrFromStore(
  startRow: number,
  sortModel: any[],
  filterModel: any,
  params: BlockDataParams
): Promise<BlockDataResponse[]> {
  
  const payload = {
    connectionId: params.connectionId,
    dataSourceType: params.dataSourceType,
    operatingObject: params.operatingObject,
    databaseName: params.databaseName,
    statements: [params.statement],
    tabKey: params.queryKey,
    startRow,
    sortModel,
    filterModel
  }

  try {
    const response = await executeSqlSegment(payload)
    
    // 处理响应数据结构
    const result: BlockDataResponse = {
      resultData: response.messageData || [],
      detailedResultData: response.messageData || [],
      executeError: response.executeStatus === 'ERROR' ? { message: response.errorMessage } : null,
      supportPage: true, // 默认支持分页
      databaseName: params.databaseName,
      connectionId: params.connectionId,
      dataSourceType: params.dataSourceType,
      operatingObject: params.operatingObject,
      statementObject: params.operatingObject
    }

    return [result]
  } catch (error) {
    console.error('获取块数据失败:', error)
    throw error
  }
}

/**
 * Oracle 自定义字段扩展处理
 * @param detailedResultData 详细结果数据
 * @returns 扩展后的数据
 */
export function getExpandedCurResultData(detailedResultData: any[]): any[] {
  return detailedResultData.map((curItem: any) => {
    const customCurItem = Object.assign({ editable: true }, curItem)
    const keys = Object.keys(curItem)

    for (const key of keys) {
      const value = curItem[key]

      // Oracle 特殊处理逻辑
      if (value?.cursorValue !== undefined) {
        customCurItem[key] = value
      } else {
        customCurItem[key] = value?.value
      }

      if (value?.formatValue) {
        customCurItem[`get${key}`] = () => value.formatValue
      }

      if (value?.renderType?.includes('binary') && value?.size) {
        customCurItem[`${key}CQSize`] = value.size
      }

      if (!value?.editable) {
        customCurItem.editable = false
      }
    }

    return customCurItem
  })
}

/**
 * 数据验证辅助函数
 * @param data 待验证的数据
 * @returns 验证结果
 */
export function validateRowData(data: any): boolean {
  if (!data || typeof data !== 'object') {
    return false
  }
  
  // 基本的数据结构验证
  return true
}

/**
 * 计算分页信息
 * @param currentPage 当前页码
 * @param pageSize 页面大小
 * @param totalCount 总数量（可选）
 * @returns 分页信息
 */
export function calculatePaginationInfo(
  currentPage: number,
  pageSize: number,
  totalCount?: number
) {
  const startRow = (currentPage - 1) * pageSize + 1
  const endRow = currentPage * pageSize
  
  return {
    startRow,
    endRow,
    currentPage,
    pageSize,
    totalPages: totalCount ? Math.ceil(totalCount / pageSize) : null,
    hasNextPage: totalCount ? currentPage < Math.ceil(totalCount / pageSize) : null
  }
}