import { formatAndModifyResult, getCompleteCell } from 'src/api'

interface ModifyParams {
  connectionId: string
  dataSourceType: string
  operatingObject: string
  databaseName: string
  statements: string[]
  tabKey: string
}

interface OperationResult {
  success: boolean
  data?: any
  error?: string
}

/**
 * Grid 数据操作服务
 * 封装所有与数据 CRUD 操作相关的逻辑
 */
export class GridOperationsService {
  
  /**
   * 插入数据
   * @param rowData 要插入的行数据
   * @param params 连接参数
   * @returns 操作结果
   */
  static async insertRow(rowData: any, params: ModifyParams): Promise<OperationResult> {
    try {
      const modifyParams = {
        ...params,
        operations: [
          { resultOldData: {}, resultNewData: rowData, resultOperating: 'INSERT' }
        ]
      }
      
      const statements = await formatAndModifyResult(modifyParams)
      const executeParams = {
        ...params,
        statements: statements || []
      }
      
      const result = await getCompleteCell(executeParams)
      
      return {
        success: true,
        data: result
      }
    } catch (error) {
      console.error('插入数据失败:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : '插入失败'
      }
    }
  }

  /**
   * 更新数据
   * @param oldData 原始数据
   * @param newData 新数据
   * @param params 连接参数
   * @returns 操作结果
   */
  static async updateRow(oldData: any, newData: any, params: ModifyParams): Promise<OperationResult> {
    try {
      const modifyParams = {
        ...params,
        operations: [
          { resultOldData: oldData, resultNewData: newData, resultOperating: 'UPDATE' }
        ]
      }
      
      const statements = await formatAndModifyResult(modifyParams)
      const executeParams = {
        ...params,
        statements: statements || []
      }
      
      const result = await getCompleteCell(executeParams)
      
      return {
        success: true,
        data: result
      }
    } catch (error) {
      console.error('更新数据失败:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : '更新失败'
      }
    }
  }

  /**
   * 删除数据
   * @param rowData 要删除的行数据
   * @param params 连接参数
   * @returns 操作结果
   */
  static async deleteRow(rowData: any, params: ModifyParams): Promise<OperationResult> {
    try {
      const modifyParams = {
        ...params,
        operations: [
          { resultOldData: rowData, resultNewData: {}, resultOperating: 'DELETE' }
        ]
      }
      
      const statements = await formatAndModifyResult(modifyParams)
      const executeParams = {
        ...params,
        statements: statements || []
      }
      
      const result = await getCompleteCell(executeParams)
      
      return {
        success: true,
        data: result
      }
    } catch (error) {
      console.error('删除数据失败:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : '删除失败'
      }
    }
  }

  /**
   * 批量操作
   * @param operations 操作列表
   * @param params 连接参数
   * @returns 操作结果
   */
  static async batchOperations(
    operations: Array<{
      type: 'INSERT' | 'UPDATE' | 'DELETE'
      oldData?: any
      newData?: any
    }>,
    params: ModifyParams
  ): Promise<OperationResult> {
    try {
      const modifyParams = {
        ...params,
        operations: operations.map(op => ({
          resultOldData: op.oldData || {},
          resultNewData: op.newData || {},
          resultOperating: op.type
        }))
      }
      
      const statements = await formatAndModifyResult(modifyParams)
      const executeParams = {
        ...params,
        statements: statements || []
      }
      
      const result = await getCompleteCell(executeParams)
      
      return {
        success: true,
        data: result
      }
    } catch (error) {
      console.error('批量操作失败:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : '批量操作失败'
      }
    }
  }

  /**
   * 验证行数据
   * @param rowData 行数据
   * @param isInsert 是否为插入操作
   * @returns 验证结果
   */
  static validateRowData(rowData: any, isInsert: boolean = false): { valid: boolean; errors: string[] } {
    const errors: string[] = []
    
    if (!rowData || typeof rowData !== 'object') {
      errors.push('行数据不能为空')
      return { valid: false, errors }
    }
    
    // 检查必填字段
    const requiredFields = Object.keys(rowData).filter(key => {
      const value = rowData[key]
      return value?.required === true
    })
    
    for (const field of requiredFields) {
      const value = rowData[field]
      if (value?.value === undefined || value?.value === null || value?.value === '') {
        errors.push(`字段 ${field} 为必填项`)
      }
    }
    
    // 检查数据类型
    Object.keys(rowData).forEach(key => {
      const fieldData = rowData[key]
      if (fieldData?.dataType && fieldData?.value !== undefined && fieldData?.value !== null) {
        if (!this.validateDataType(fieldData.value, fieldData.dataType)) {
          errors.push(`字段 ${key} 的数据类型不匹配`)
        }
      }
    })
    
    return { valid: errors.length === 0, errors }
  }

  /**
   * 验证数据类型
   * @param value 值
   * @param dataType 数据类型
   * @returns 是否有效
   */
  private static validateDataType(value: any, dataType: string): boolean {
    switch (dataType.toUpperCase()) {
      case 'INT':
      case 'INTEGER':
      case 'BIGINT':
        return Number.isInteger(Number(value))
      
      case 'FLOAT':
      case 'DOUBLE':
      case 'DECIMAL':
        return !isNaN(Number(value))
      
      case 'VARCHAR':
      case 'TEXT':
      case 'STRING':
        return typeof value === 'string'
      
      case 'DATE':
      case 'DATETIME':
      case 'TIMESTAMP':
        return !isNaN(Date.parse(value))
      
      case 'BOOLEAN':
        return typeof value === 'boolean' || value === 'true' || value === 'false'
      
      default:
        return true // 对于不识别的类型，默认通过验证
    }
  }

  /**
   * 清理行数据，移除内部字段
   * @param rowData 行数据
   * @returns 清理后的数据
   */
  static cleanRowData(rowData: any): any {
    const cleaned = { ...rowData }
    
    // 移除内部字段
    delete cleaned._isNewRow
    delete cleaned._isDeleted
    delete cleaned._rowId
    delete cleaned.editable
    
    return cleaned
  }
}