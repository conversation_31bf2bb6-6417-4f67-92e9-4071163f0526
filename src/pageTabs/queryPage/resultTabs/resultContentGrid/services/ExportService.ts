import { makeResultAllExport, makeResultExport, type ExportFormat } from 'src/api'
import type { GridApi } from '@ag-grid-community/core'

interface ExportParams {
  connectionId: string
  connectionType: string
  databaseName: string
  operatingObject: string
  statement: string
  containTempTable?: boolean
  tabKey: string
}

interface ExportOptions {
  exportFormat?: ExportFormat
  fileName?: string
  includeHeader?: boolean
  maxRows?: number
  resultNum?: number
  exportDataNum?: number
}

interface ExportResult {
  success: boolean
  taskId?: string
  error?: string
}

/**
 * 导出服务
 * 封装所有与数据导出相关的逻辑
 */
export class ExportService {
  
  /**
   * 导出全量数据
   * @param params 导出参数
   * @param options 导出选项
   * @returns 导出结果
   */
  static async exportAllData(params: ExportParams, options: ExportOptions = {}): Promise<ExportResult> {
    try {
      const exportData = {
        ...params,
        ...options,
        // 提供必需的默认值
        exportFormat: options.exportFormat || 'EXCEL' as ExportFormat,
        resultNum: options.resultNum || 0,
        exportDataNum: options.exportDataNum || 0,
        containTempTable: params.containTempTable || false
      }

      const result = await makeResultAllExport(exportData as any)

      return {
        success: true,
        taskId: result.messageId // QueryParams 返回的是 messageId，不是 taskId
      }
    } catch (error) {
      console.error('全量导出失败:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : '导出失败'
      }
    }
  }

  /**
   * 导出选中的数据
   * @param gridApi AG-Grid API
   * @param params 导出参数
   * @param options 导出选项
   * @returns 导出结果
   */
  static async exportSelectedData(
    gridApi: GridApi,
    params: ExportParams,
    options: ExportOptions = {}
  ): Promise<ExportResult> {
    try {
      // 获取选中的行数据
      const selectedRows = gridApi.getSelectedRows()
      
      if (selectedRows.length === 0) {
        return {
          success: false,
          error: '请先选择要导出的数据行'
        }
      }

      const exportData = {
        ...params,
        ...options,
        selectedData: selectedRows,
        // 提供必需的默认值
        exportFormat: options.exportFormat || 'EXCEL' as ExportFormat,
        resultNum: options.resultNum || 0,
        exportDataNum: options.exportDataNum || 0,
        containTempTable: params.containTempTable || false
      }

      const result = await makeResultExport(exportData as any)

      return {
        success: true,
        taskId: result.messageId
      }
    } catch (error) {
      console.error('选中数据导出失败:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : '导出失败'
      }
    }
  }

  /**
   * 导出当前页数据
   * @param currentPageData 当前页数据
   * @param params 导出参数
   * @param options 导出选项
   * @returns 导出结果
   */
  static async exportCurrentPageData(
    currentPageData: any[],
    params: ExportParams,
    options: ExportOptions = {}
  ): Promise<ExportResult> {
    try {
      const exportData = {
        ...params,
        ...options,
        currentPageData,
        // 提供必需的默认值
        exportFormat: options.exportFormat || 'EXCEL' as ExportFormat,
        resultNum: options.resultNum || 0,
        exportDataNum: options.exportDataNum || 0,
        containTempTable: params.containTempTable || false
      }

      const result = await makeResultExport(exportData as any)

      return {
        success: true,
        taskId: result.messageId
      }
    } catch (error) {
      console.error('当前页导出失败:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : '导出失败'
      }
    }
  }

  /**
   * 导出过滤后的数据
   * @param gridApi AG-Grid API
   * @param params 导出参数
   * @param options 导出选项
   * @returns 导出结果
   */
  static async exportFilteredData(
    gridApi: GridApi,
    params: ExportParams,
    options: ExportOptions = {}
  ): Promise<ExportResult> {
    try {
      // 获取当前的过滤模型
      const filterModel = gridApi.getFilterModel()
      // 修复 getSortModel 方法名
      const sortModel = (gridApi as any).getSortModel?.() || []

      const exportData = {
        ...params,
        ...options,
        filterModel,
        sortModel,
        // 提供必需的默认值
        exportFormat: options.exportFormat || 'EXCEL' as ExportFormat,
        resultNum: options.resultNum || 0,
        exportDataNum: options.exportDataNum || 0,
        containTempTable: params.containTempTable || false
      }

      const result = await makeResultAllExport(exportData as any)

      return {
        success: true,
        taskId: result.messageId
      }
    } catch (error) {
      console.error('过滤数据导出失败:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : '导出失败'
      }
    }
  }

  /**
   * 生成导出文件名
   * @param prefix 前缀
   * @param format 格式
   * @returns 文件名
   */
  static generateFileName(prefix: string = 'export', format: string = 'csv'): string {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-').slice(0, -5)
    return `${prefix}_${timestamp}.${format}`
  }

  /**
   * 验证导出参数
   * @param params 导出参数
   * @returns 验证结果
   */
  static validateExportParams(params: ExportParams): { valid: boolean; errors: string[] } {
    const errors: string[] = []
    
    if (!params.connectionId) {
      errors.push('连接ID不能为空')
    }
    
    if (!params.connectionType) {
      errors.push('连接类型不能为空')
    }
    
    if (!params.databaseName) {
      errors.push('数据库名不能为空')
    }
    
    if (!params.operatingObject) {
      errors.push('操作对象不能为空')
    }
    
    if (!params.statement) {
      errors.push('SQL语句不能为空')
    }
    
    if (!params.tabKey) {
      errors.push('标签页Key不能为空')
    }
    
    return { valid: errors.length === 0, errors }
  }

  /**
   * 格式化导出数据
   * @param data 原始数据
   * @param columns 列定义
   * @returns 格式化后的数据
   */
  static formatExportData(data: any[], columns?: any[]): any[] {
    if (!columns || columns.length === 0) {
      return data
    }
    
    return data.map(row => {
      const formattedRow: any = {}
      
      columns.forEach(col => {
        const field = col.field || col.colId
        const value = row[field]
        
        // 处理特殊值类型
        if (value?.value !== undefined) {
          formattedRow[field] = value.value
        } else if (value?.formatValue) {
          formattedRow[field] = value.formatValue
        } else {
          formattedRow[field] = value
        }
      })
      
      return formattedRow
    })
  }

  /**
   * 获取支持的导出格式
   * @returns 支持的格式列表
   */
  static getSupportedFormats(): Array<{ value: string; label: string }> {
    return [
      { value: 'csv', label: 'CSV 文件' },
      { value: 'excel', label: 'Excel 文件' },
      { value: 'json', label: 'JSON 文件' },
      { value: 'sql', label: 'SQL 脚本' }
    ]
  }

  /**
   * 估算导出数据大小
   * @param rowCount 行数
   * @param columnCount 列数
   * @param avgCellSize 平均单元格大小（字节）
   * @returns 估算大小（字节）
   */
  static estimateExportSize(rowCount: number, columnCount: number, avgCellSize: number = 50): number {
    return rowCount * columnCount * avgCellSize
  }
}