import React, { useState, useEffect, useMemo, useCallback, useRef } from 'react'
import { Spin } from 'antd'
import { useDispatch } from 'src/hook'
import { useTranslation } from 'react-i18next'
import styles from './grid.module.scss'

// 导入重构后的 hooks
import { useGridData } from './hooks/useGridData'
import { useGridOperations } from './hooks/useGridOperations'  
import { useGridConfig } from './hooks/useGridConfig'
import { useExportOperations } from './hooks/useExportOperations'
import { useGridKeyboardHandlers } from './hooks/useGridKeyboardHandlers'
import { useSearch } from './hooks/useSearch'

// 组件
import { GridContainer } from './components/GridContainer'
import { GridModals } from './components/GridModals'
import { ResultToolbar } from './ResultToolbar'
import ResultSearchBar from './ResultSearchBar'

// 导入现有服务和工具
import { useSelectionController } from './hooks/useSelectionController'
import { useGridStatus } from './useGridStatus'
import { ContextMenuHandlers } from './services/ContextMenuHandlers'
import { Iconfont } from 'src/components'

// 导入类型
import type { ResultGridPaginationProps } from './types/index'

// 定义简化的网格上下文接口
interface SimpleGridContext {
  canSave: boolean
  status: 'INSERT' | 'DELETE' | 'CLONE' | 'UPDATE' | 'NORMAL'
  disableContextMenu: boolean
  copyable: boolean
  canCopyCell: boolean
  allowClone: boolean
  allowCreateSql: boolean
  createSqlDisabled: boolean
  queryKey: string
  selectionContext?: any
  [key: string]: any
}

const ResultGridPaginationRefactored: React.FC<ResultGridPaginationProps> = (props) => {
  const { t } = useTranslation()
  const dispatch = useDispatch()
  
  // 从 props 中解构必要的数据
  const {
    result = {},
    columnInfos = [],
    theme = 'light',
    scale,
    rowNum = 100,
    editable = false,
    canCopy = true,
    refreshable = true,
    paneType,
    type,
    isExplain = false,
    permissionResult = {},
    dataExport = {},
    filterNames = [],
    txMode = 'auto',
    isDesensitized = false,
    maskedPathMap = {},
    tabInfoMap = {},
    activeTabKey = '',
    doubleCheckType,
    isDesensitizedPlaintext = false,
    detailedResultData = [],
    currentRusultData = [],
    copySetting = false
  } = props

  // 从 result 中解构连接信息
  const {
    connectionId = '',
    dataSourceType = '',
    operatingObject = '',
    databaseName = '',
    statement = '',
    tabKey: queryKey = '',
    connectionType = ''
  } = result

  // 基础状态
  const [sortModel, setSortModel] = useState<any[]>([])
  const [filterModel, setFilterModel] = useState<any>({})
  const [filterSql, setFilterSql] = useState<string | null>(null)
  const [rowViewerResultData, setRowViewerResultData] = useState<any[]>([])
  
  // 弹窗状态
  const [visibleCellViewer, setVisibleCellViewer] = useState(false)
  const [visibleRowViewer, setVisibleRowViewer] = useState(false)
  const [visibleDesensitizedFields, setVisibleDesensitizedFields] = useState(false)

  // Refs
  const copyableRef = useRef<HTMLDivElement>(null)
  const contextMenuHandlersRef = useRef<ContextMenuHandlers | null>(null)
  const customSelectionControllerRef = useRef<any>(null)
  
  // 使用网格状态管理
  const { dispatchAction, status } = useGridStatus(queryKey)

  // 1. 使用数据管理 Hook
  const gridData = useGridData({
    gridApiRef: useRef(null),
    connectionId,
    dataSourceType,
    operatingObject,
    databaseName,
    statement,
    queryKey,
    columnDefs: [], // 将在 gridConfig 中处理
    rowNum,
    isDesensitizedPlaintext,
    detailedResultData,
    currentRusultData,
    dispatchAction,
    retriggerSearchAfterPagination: () => {} // 将在搜索 hook 中处理
  })

  // 2. 使用搜索功能 Hook  
  const search = useSearch({ 
    gridApiRef: gridData.cachePageResultDataRef.current ? { current: null } : { current: null }
  })

  // 3. 使用网格配置 Hook
  const gridConfig = useGridConfig({
    columnInfos,
    theme,
    scale,
    searchHighlightRefs: search.searchHighlightRefs,
    rowNum,
    editable,
    canCopy
  })

  // 4. 使用操作管理 Hook
  const gridOperations = useGridOperations({
    gridApiRef: gridConfig.gridApiRef,
    connectionId,
    dataSourceType,
    operatingObject,
    databaseName,
    queryKey,
    editable,
    connectionType,
    cachePageResultDataRef: gridData.cachePageResultDataRef,
    dispatchAction,
    handleRefresh: gridData.handleRefresh
  })

  // 5. 使用导出操作 Hook
  const exportOps = useExportOperations({
    result: {
      connectionId,
      dataSourceType,
      databaseName,
      operatingObject,
      statement,
      tabKey: queryKey
    },
    permissionResult,
    dataExport
  })

  // 6. 使用键盘处理 Hook
  const keyboardHandlers = useGridKeyboardHandlers({
    gridApiRef: gridConfig.gridApiRef,
    setVisibleCellViewer,
    setVisibleRowViewer,
    setFocusedColumn: () => {},
    setFocusedRowIndex: () => {},
    setFocusedRowData: () => {}
  })

  // 使用选择控制器
  const {
    customSelectionControllerRef: selectionControllerRef,
    contextHandlersReady,
    selectionContext,
    updateSelectionContext,
    cleanupSelectionController
  } = useSelectionController({
    gridApiRef: gridConfig.gridApiRef,
    isScrollLoading: gridData.loading
  })

  // 清理选择控制器
  useEffect(() => {
    return cleanupSelectionController
  }, [cleanupSelectionController])

  // 网格就绪处理
  const handleGridReady = useCallback((event: any) => {
    const { api, columnApi } = event
    gridConfig.gridApiRef.current = { api, columnApi }
    
    // 设置数据源
    if (gridData.commonGetRows) {
      const datasource = {
        getRows: (params: any) => {
          gridData.commonGetRows(params, false)
        }
      }
      gridConfig.setDatasource(datasource)
    }
  }, [gridData.commonGetRows, gridConfig])

  // 处理排序变化
  const handleSortChanged = useCallback(() => {
    setRowViewerResultData([])
    const newSortModel = gridConfig.gridApiRef?.current?.columnApi
      ?.getColumnState()
      ?.filter((col: any) => col.sort)
      ?.map((col: any) => ({ colId: col.colId, sort: col.sort })) || []
    setSortModel(newSortModel)
  }, [gridConfig.gridApiRef])

  // 处理过滤变化
  const handleFilterChanged = useCallback(() => {
    setRowViewerResultData([])
    const newFilterModel = gridConfig.gridApiRef?.current?.api?.getFilterModel()
    setFilterModel(newFilterModel || {})
    setFilterSql(null) // 重置 filterSql
    
    // 过滤条件变化时，重置所有分页相关状态
    gridData.setHasNextPage(false)
    gridData.setCurPagination(prev => ({
      ...prev,
      pageNumber: 1,
      total: null,
      maxNumber: null,
      pageSize: prev?.pageSize || rowNum
    }))
    
    // 重置缓存的分页数据
    gridData.cachePageResultDataRef.current = []
  }, [gridConfig.gridApiRef, gridData, rowNum])

  // 使用 loadPage 效果
  const sortModelStr = JSON.stringify(sortModel)
  const filterModelStr = JSON.stringify(filterModel)
  
  useEffect(() => {
    if (gridData.curPagination?.pageNumber) {
      gridData.loadPage(
        gridData.curPagination.pageNumber,
        gridData.curPagination.pageSize,
        sortModel,
        filterModel
      )
    }
  }, [
    gridData.curPagination?.pageNumber,
    gridData.curPagination?.pageSize,
    sortModelStr,
    filterModelStr,
    gridData
  ])

  // 构建 AG-Grid 上下文
  const agContext = useMemo<SimpleGridContext>(() => {
    return {
      canSave: gridOperations.canSave,
      status,
      disableContextMenu: paneType === 'tSql',
      copyable: canCopy,
      canCopyCell: canCopy,
      allowClone: !(connectionType === 'DB2' || connectionType === 'StarRocks' || connectionType === 'Inceptor') && editable,
      allowCreateSql: permissionResult?.supportResultSetSqlGenerator || false,
      createSqlDisabled: permissionResult?.resultSetSqlGenerator || false,
      handleCopyCell: () => {},
      handleCopyRow: () => {},
      handleCopyAll: () => {},
      handleCopyCellTitle: () => {},
      handleCopyAllTitle: () => {},
      handleCopyWithTitle: () => {},
      handlePasteRow: gridOperations.handlePasteRow,
      handleViewCell: keyboardHandlers.handleToolbarView,
      handleViewRow: keyboardHandlers.handleToolbarView,
      handleCloneRow: gridOperations.handleCloneRow,
      handleResInsert: gridOperations.handleResInsert,
      handleResUpdate: gridOperations.handleResUpdate,
      handleResDelete: gridOperations.handleResDelete,
      handleCopySelectAllWithTitle: () => {},
      handleCopySelectedColumnTitles: contextHandlersReady ? contextMenuHandlersRef.current?.handleCopySelectedColumnTitles : undefined,
      handleCopySelectedColumnData: contextHandlersReady ? contextMenuHandlersRef.current?.handleCopySelectedColumnData : undefined,
      handleCopySelectedCellsData: contextHandlersReady ? contextMenuHandlersRef.current?.handleCopySelectedCellsData : undefined,
      handleCopySelectedCellsTitles: contextHandlersReady ? contextMenuHandlersRef.current?.handleCopySelectedCellsTitles : undefined,
      handleCopySelectedAllCellsTitles: () => {},
      handleCopySelectedCellsWithTitle: contextHandlersReady ? contextMenuHandlersRef.current?.handleCopySelectedCellsWithTitle : undefined,
      handleCopySelectedFullColumnDataWithTitle: contextHandlersReady ? contextMenuHandlersRef.current?.handleCopySelectedFullColumnDataWithTitle : undefined,
      getSelectedRowsDataForResultSet: contextHandlersReady ? contextMenuHandlersRef.current?.getSelectedRowsDataForResultSet : undefined,
      getSelectionContext: () => selectionContext,
      selectCell: (params: any) => {
        if (customSelectionControllerRef.current && params.column && params.rowIndex != null) {
          const colId = params.column.getColId()
          customSelectionControllerRef.current.selectCell(params.rowIndex, colId)
          updateSelectionContext()
        }
      },
      selectionContext: selectionContext,
      queryKey: queryKey,
    }
  }, [
    gridOperations.canSave,
    status,
    paneType,
    canCopy,
    connectionType,
    editable,
    permissionResult,
    gridOperations,
    keyboardHandlers,
    contextHandlersReady,
    selectionContext,
    updateSelectionContext,
    queryKey
  ])

  // 处理复制设置副作用
  useEffect(() => {
    if (copySetting) return
    if (visibleRowViewer || visibleCellViewer) {
      document.oncopy = (e) => {
        e.returnValue = false
      }
      document.oncontextmenu = (e) => {
        e.returnValue = false
      }
    } else {
      document.oncopy = (e) => {
        e.returnValue = true
      }
      document.oncontextmenu = (e) => {
        e.returnValue = true
      }
    }
  }, [visibleRowViewer, visibleCellViewer, copySetting])

  // 渲染主要内容
  return (
    <div className={styles.resultGrid}>
      <div className={styles.resultContent} ref={copyableRef}>
        {!columnInfos || columnInfos.length === 0 ? (
          <div className={styles.resultGrid__filter}>
            <Iconfont type='icon-yizhongzhi' style={{ marginRight: '4px' }} />
            {t('sdo_result_empty_filtered_field')}：{filterNames?.join(',')}
          </div>
        ) : (
          <Spin spinning={gridData.loading} wrapperClassName={styles.gridSpinContainer}>
            <ResultToolbar
              enabledActions={gridOperations.enabledActions}
              paneType={paneType}
              onRefresh={gridData.handleRefresh}
              onAddRow={gridOperations.handleAddRow}
              onDeleteRow={gridOperations.handleDeleteRowConfirm}
              onConfirmModify={gridOperations.handleConfirmModify}
              onScalePage={() => {}}
              onCancelModify={gridOperations.handleCancelModify}
              onViewCell={keyboardHandlers.handleToolbarView}
              readOnly={!editable}
              refreshable={refreshable}
              isDataExportPermissionEnabled={exportOps.isDataExportPermissionEnabled}
              applyDataExportPermission={exportOps.applyDataExportPermission}
              onOpenExportModal={exportOps.onOpenExportModal}
              showExported={dataExport.showExported}
              connectionType={connectionType}
              executePayload={{
                connectionId,
                dataSourceType,
                operatingObject,
                databaseName,
                statements: [statement],
                tabKey: queryKey,
              }}
              scale={scale === undefined ? 100 : scale}
              type={type}
              permissionResult={permissionResult}
              dataExport={dataExport}
              filterNames={filterNames}
              txMode={txMode}
              onOpenFieldsModal={() => { setVisibleDesensitizedFields(true) }}
              isDesensitized={isDesensitized}
              onSave={gridOperations.handleSave}
              onApplyDensens={() => {}}
              maskedPathMap={maskedPathMap}
              onSearch={search.handleSearch}
              setVisibleSearchBox={search.setSearchVisible}
              isSearchActive={search.searchVisible}
              showPagination={true}
              currentPageNumber={gridData.paginationData.currentPageNumber}
              pageSize={gridData.paginationData.pageSize}
              startRowInPage={gridData.paginationData.startRowInPage}
              endRowInPage={gridData.paginationData.endRowInPage}
              hasNextPage={gridData.paginationData.hasNextPage}
              isLoading={gridData.paginationData.isLoading}
              onPageChange={gridData.handlePageChange}
              onPageSizeChange={gridData.handlePageSizeChange}
              totalFromParent={gridData.paginationData.total}
              settingPageSize={tabInfoMap[activeTabKey]?.settingPageSize}
              filterSql={filterSql}
            />
            
            {search.searchVisible && (
              <ResultSearchBar
                visible={search.searchVisible}
                totalMatches={search.totalMatches}
                currentMatch={search.currentMatchIndex + 1}
                onSearch={search.handleSearch}
                onNavigate={search.handleSearchNavigation}
                onQuickFilter={search.handleQuickFilter}
                setNeedFullUpdate={search.setNeedFullUpdate}
                onClose={() => {
                  search.setSearchVisible(false)
                  search.clearAllHighlights()
                }}
                needFullUpdateRef={search.needFullUpdateRef}
              />
            )}
            
            <GridContainer
              gridConfig={gridConfig.gridConfig}
              infiniteModeOptions={gridConfig.infiniteModeOptions}
              columnDefs={gridConfig.columnDefs}
              defaultColDef={gridConfig.defaultColDef}
              datasource={gridConfig.datasource}
              theme={theme}
              canCopy={canCopy}
              tabResultKey={queryKey}
              aggridKey={0}
              curPagination={gridData.curPagination}
              rowNum={rowNum}
              agContext={agContext}
              onGridReady={handleGridReady}
              onCellFocused={keyboardHandlers.handleCellFocused}
              onCellKeyDown={keyboardHandlers.onCellKeyDown}
              onCellClicked={keyboardHandlers.handleCellClicked}
              onCellContextMenu={(params) => {
                if (contextMenuHandlersRef.current) {
                  contextMenuHandlersRef.current.handleBeforeContextMenu(params)
                }
              }}
              onRowEditingStarted={gridOperations.handleStartRowEditing}
              onRowEditingStopped={gridOperations.handleStopRowEditing}
              onSelectionChanged={() => {}}
              onSortChanged={handleSortChanged}
              onFilterChanged={handleFilterChanged}
              onFirstDataRendered={gridConfig.autoSizeColumns}
            />
          </Spin>
        )}
      </div>
      
      <GridModals
        gridApi={gridConfig.gridApiRef.current?.api || null}
        focusedRowIndex={keyboardHandlers.focusedRowIndex}
        focusedRowData={keyboardHandlers.focusedRowData}
        result={result}
        detailedResultData={detailedResultData}
        editable={editable}
        connectionType={connectionType}
        permissionResult={permissionResult}
        canCopyCell={canCopy}
        type={type}
        isExplain={isExplain}
        visibleCellViewer={visibleCellViewer}
        visibleRowViewer={visibleRowViewer}
        visibleSelectedExport={exportOps.visibleSelectedExport}
        visibleCommonExport={exportOps.visibleCommonExport}
        visibleExportAll={exportOps.visibleExportAll}
        visibleDesensitizedFields={visibleDesensitizedFields}
        setVisibleCellViewer={setVisibleCellViewer}
        setVisibleRowViewer={setVisibleRowViewer}
        setVisibleSelectedExport={exportOps.setVisibleSelectedExport}
        setVisibleCommonExport={exportOps.setVisibleCommonExport}
        setVisibleExportAll={exportOps.setVisibleExportAll}
        setVisibleDesensitizedFields={setVisibleDesensitizedFields}
        updateFocusedCell={() => {}}
        downloadFocusedCell={() => {}}
        fetchFocusedCell={async () => []}
        handleExportAllResult={exportOps.handleExportAllResult}
        applyDataExportPermission={exportOps.applyDataExportPermission}
        rowViewerResultData={rowViewerResultData}
        curPagination={gridData.curPagination}
        rowNum={rowNum}
        setRowViewerResultData={setRowViewerResultData}
        nextRowIndex={keyboardHandlers.nextRowIndex}
        lastRowIndex={keyboardHandlers.lastRowIndex}
        doubleCheckType={doubleCheckType as 'NONE' | 'SMS' | 'OTP' | null | undefined}
        filterNames={filterNames}
      />
    </div>
  )
}

export default ResultGridPaginationRefactored