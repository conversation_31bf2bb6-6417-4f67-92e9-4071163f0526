import { useState, useCallback } from 'react'
import { useDispatch } from 'src/hook'
// import { openFlowForm } from 'src/store/slice/flow' // 临时注释掉，避免编译错误
import { makeResultAllExport } from 'src/api'
import Service from 'src/service'

interface ExportResult {
  connectionId: string
  dataSourceType: string
  databaseName: string
  operatingObject: string
  statement: string
  containTempTable?: boolean
  tabKey: string
}

interface UseExportOperationsProps {
  result: ExportResult
  permissionResult?: any
  dataExport?: any
}

interface UseExportOperationsReturn {
  // 弹窗状态
  visibleCommonExport: boolean
  visibleSelectedExport: boolean
  visibleExportAll: boolean
  
  // 弹窗控制
  setVisibleCommonExport: React.Dispatch<React.SetStateAction<boolean>>
  setVisibleSelectedExport: React.Dispatch<React.SetStateAction<boolean>>
  setVisibleExportAll: React.Dispatch<React.SetStateAction<boolean>>
  
  // 导出权限
  isDataExportPermissionEnabled: boolean
  
  // 导出操作
  onOpenExportModal: (key: string) => void
  applyDataExportPermission: (exportType?: string) => Promise<void>
  handleExportAllResult: (data: any) => Promise<void>
  
  // 通知方法
  exportTaskCreatedNot: () => void
}

export function useExportOperations({
  result,
  permissionResult,
  dataExport
}: UseExportOperationsProps): UseExportOperationsReturn {
  
  const dispatch = useDispatch()
  
  // 弹窗状态
  const [visibleCommonExport, setVisibleCommonExport] = useState(false)
  const [visibleSelectedExport, setVisibleSelectedExport] = useState(false)
  const [visibleExportAll, setVisibleExportAll] = useState(false)

  // 检查数据导出权限是否启用
  const isDataExportPermissionEnabled = Service.moduleService.isModuleExist('/flow')
    ? dataExport?.status
    : false

  // 导出任务创建通知
  const exportTaskCreatedNot = useCallback(() => {
    // 这里可以添加具体的通知逻辑
    console.log('导出任务已创建')
  }, [])

  // 打开导出弹窗
  const onOpenExportModal = useCallback((key: string) => {
    const visibleMap = {
      ResultExport: () => setVisibleCommonExport(true),
      SelectedResultExport: () => setVisibleSelectedExport(true),
      ResultAllExport: () => setVisibleExportAll(true),
    }
    
    if (key in visibleMap) {
      visibleMap[key as keyof typeof visibleMap]()
    }
  }, [])

  // 申请数据导出权限
  const applyDataExportPermission = useCallback(async (exportType?: string) => {
    const elements = permissionResult?.allNodePathString?.map((item: any) => ({
      nodePath: item,
      connectionType: result.dataSourceType
    }))

    // 临时注释掉 dispatch 调用，避免编译错误
    console.log('打开导出表单:', {
      type: 'dataExport',
      fields: {
        elements: elements,
        operationList: exportType ? [exportType] : undefined,
      },
    })
  }, [dispatch, permissionResult, result.dataSourceType])

  // 处理全量导出结果
  const handleExportAllResult = useCallback(async (data: any) => {
    const params = {
      connectionId: result.connectionId,
      connectionType: result.dataSourceType,
      databaseName: result.databaseName,
      operatingObject: result.operatingObject,
      statement: result.statement,
      containTempTable: result.containTempTable,
      tabKey: result.tabKey,
    }
    
    try {
      await makeResultAllExport(Object.assign({}, params, data))
      exportTaskCreatedNot()
    } catch (error) {
      console.error('导出失败:', error)
      throw error
    }
  }, [result, exportTaskCreatedNot])

  return {
    // 弹窗状态
    visibleCommonExport,
    visibleSelectedExport,
    visibleExportAll,
    
    // 弹窗控制
    setVisibleCommonExport,
    setVisibleSelectedExport,
    setVisibleExportAll,
    
    // 导出权限
    isDataExportPermissionEnabled,
    
    // 导出操作
    onOpenExportModal,
    applyDataExportPermission,
    handleExportAllResult,
    
    // 通知方法
    exportTaskCreatedNot
  }
}