import { useState, useCallback, useRef } from 'react'
import { message } from 'antd'
import { useTranslation } from 'react-i18next'
import type { GridApi, ColumnApi } from '@ag-grid-community/core'
import { useDispatch } from 'src/hook'
import { setTabExecutionStatus } from '../../../queryTabs/queryTabsSlice'
// 导入 ToolbarActionType 类型
import type { ToolbarActionType } from '../ResultToolbar'

interface UseGridOperationsProps {
  gridApiRef: React.MutableRefObject<{
    api: GridApi
    columnApi: ColumnApi
  } | null>
  connectionId: string
  dataSourceType: string
  operatingObject: string
  databaseName: string
  queryKey: string
  editable: boolean
  connectionType: string
  cachePageResultDataRef: React.MutableRefObject<any[]>
  dispatchAction: (action: string) => void
  handleRefresh: (keepFilter?: boolean) => void
}

interface UseGridOperationsReturn {
  // 编辑状态
  canSave: boolean
  enabledActions: ToolbarActionType[]
  
  // 行编辑操作
  handleAddRow: () => void
  handleDeleteRowConfirm: () => void
  handleConfirmModify: () => Promise<void>
  handleCancelModify: () => void
  handleSave: () => Promise<void>
  
  // 行数据操作
  handleCloneRow: (params: any) => void
  handleResInsert: (params: any) => Promise<void>
  handleResUpdate: (params: any) => Promise<void>  
  handleResDelete: (params: any) => Promise<void>
  handlePasteRow: (params: any) => void
  
  // 行编辑事件
  handleStartRowEditing: (event: any) => void
  handleStopRowEditing: (event: any) => void
  
  // 状态检查
  isCellEditing: () => boolean
}

export function useGridOperations({
  gridApiRef,
  connectionId,
  dataSourceType,
  operatingObject,
  databaseName,
  queryKey,
  editable,
  connectionType,
  cachePageResultDataRef,
  dispatchAction,
  handleRefresh
}: UseGridOperationsProps): UseGridOperationsReturn {
  
  const { t } = useTranslation()
  const dispatch = useDispatch()
  
  // 操作状态
  const [canSave, setCanSave] = useState(false)
  const [enabledActions, setEnabledActions] = useState<ToolbarActionType[]>([])
  
  // 编辑状态引用
  const editingStateRef = useRef<{
    insertedRows: any[]
    updatedRows: any[]
    deletedRows: any[]
  }>({
    insertedRows: [],
    updatedRows: [],
    deletedRows: []
  })

  // 检查是否有单元格正在编辑
  const isCellEditing = useCallback(() => {
    if (!gridApiRef.current) return false
    const editingCells = gridApiRef.current?.api?.getEditingCells()
    return editingCells.length > 0
  }, [gridApiRef])

  // 添加行
  const handleAddRow = useCallback(() => {
    if (!gridApiRef.current || !editable) return
    
    const { api } = gridApiRef.current
    
    // 检查是否有正在编辑的单元格
    if (isCellEditing()) {
      message.warn(t('sdo_complete_current_edit'))
      return
    }

    // 创建新行数据
    const newRowData = { 
      editable: true,
      _isNewRow: true,
      _rowId: `new_${Date.now()}`
    }
    
    // 添加到缓存数据
    cachePageResultDataRef.current = [newRowData, ...cachePageResultDataRef.current]
    
    // 刷新表格数据
    api.setRowData(cachePageResultDataRef.current)
    
    // 更新状态
    setCanSave(true)
    setEnabledActions(['save', 'cancel'])
    dispatchAction('INSERT')
    
    message.success(t('sdo_row_added'))
  }, [gridApiRef, editable, isCellEditing, t, cachePageResultDataRef, dispatchAction])

  // 删除行确认
  const handleDeleteRowConfirm = useCallback(() => {
    if (!gridApiRef.current || !editable) return
    
    const { api } = gridApiRef.current
    const selectedRows = api.getSelectedRows()
    
    if (selectedRows.length === 0) {
      message.warn(t('sdo_select_rows_to_delete'))
      return
    }

    // 检查是否有正在编辑的单元格
    if (isCellEditing()) {
      message.warn(t('sdo_complete_current_edit'))
      return
    }

    // 标记行为删除状态
    selectedRows.forEach(row => {
      row._isDeleted = true
    })
    
    // 更新编辑状态
    editingStateRef.current.deletedRows.push(...selectedRows)
    
    // 从显示数据中移除
    const remainingData = cachePageResultDataRef.current.filter(
      row => !selectedRows.includes(row)
    )
    cachePageResultDataRef.current = remainingData
    
    // 更新表格
    api.setRowData(remainingData)
    
    // 更新状态
    setCanSave(true)
    setEnabledActions(['save', 'cancel'])
    dispatchAction('DELETE')
    
    message.success(t('sdo_rows_marked_for_deletion'))
  }, [gridApiRef, editable, isCellEditing, t, cachePageResultDataRef, dispatchAction])

  // 确认修改
  const handleConfirmModify = useCallback(async () => {
    if (!canSave) return
    
    try {
      await handleSave()
      message.success(t('sdo_changes_saved'))
    } catch (error) {
      console.error('保存失败:', error)
      message.error(t('sdo_save_failed'))
    }
  }, [canSave, t])

  // 取消修改
  const handleCancelModify = useCallback(() => {
    // 重置编辑状态
    editingStateRef.current = {
      insertedRows: [],
      updatedRows: [],
      deletedRows: []
    }
    
    // 重新加载数据
    handleRefresh()
    
    // 重置状态
    setCanSave(false)
    setEnabledActions([])
    dispatchAction('reset')
    
    message.info(t('sdo_changes_cancelled'))
  }, [handleRefresh, dispatchAction, t])

  // 保存操作
  const handleSave = useCallback(async () => {
    if (!canSave) return
    
    const { insertedRows, updatedRows, deletedRows } = editingStateRef.current
    
    try {
      // 处理插入
      if (insertedRows.length > 0) {
        // 实现插入逻辑
        console.log('插入行:', insertedRows)
      }
      
      // 处理更新
      if (updatedRows.length > 0) {
        // 实现更新逻辑
        console.log('更新行:', updatedRows)
      }
      
      // 处理删除
      if (deletedRows.length > 0) {
        // 实现删除逻辑
        console.log('删除行:', deletedRows)
      }
      
      // 重置状态
      editingStateRef.current = {
        insertedRows: [],
        updatedRows: [],
        deletedRows: []
      }
      
      setCanSave(false)
      setEnabledActions([])
      dispatchAction('reset')
      
      // 刷新数据
      handleRefresh()
      
      // 重置执行状态
      dispatch(setTabExecutionStatus({ key: queryKey, pending: false }))
      
    } catch (error) {
      console.error('保存操作失败:', error)
      throw error
    }
  }, [canSave, queryKey, dispatch, dispatchAction, handleRefresh])

  // 克隆行
  const handleCloneRow = useCallback((params: any) => {
    if (!gridApiRef.current || !editable) return
    
    const { api } = gridApiRef.current
    const { data } = params
    
    // 创建克隆数据
    const clonedData = {
      ...data,
      _isNewRow: true,
      _rowId: `clone_${Date.now()}`
    }
    
    // 删除不应该克隆的字段
    delete clonedData._isDeleted
    
    // 添加到缓存数据
    cachePageResultDataRef.current = [clonedData, ...cachePageResultDataRef.current]
    
    // 更新表格
    api.setRowData(cachePageResultDataRef.current)
    
    // 更新状态
    setCanSave(true)
    setEnabledActions(['save', 'cancel'])
    dispatchAction('CLONE')
    
    message.success(t('sdo_row_cloned'))
  }, [gridApiRef, editable, cachePageResultDataRef, dispatchAction, t])

  // 插入操作
  const handleResInsert = useCallback(async (params: any) => {
    const { data } = params
    editingStateRef.current.insertedRows.push(data)
    setCanSave(true)
    setEnabledActions(['save', 'cancel'])
  }, [])

  // 更新操作
  const handleResUpdate = useCallback(async (params: any) => {
    const { data } = params
    editingStateRef.current.updatedRows.push(data)
    setCanSave(true)
    setEnabledActions(['save', 'cancel'])
  }, [])

  // 删除操作
  const handleResDelete = useCallback(async (params: any) => {
    const { data } = params
    editingStateRef.current.deletedRows.push(data)
    setCanSave(true)
    setEnabledActions(['save', 'cancel'])
  }, [])

  // 粘贴行
  const handlePasteRow = useCallback((params: any) => {
    if (!editable) return
    // 实现粘贴逻辑
    console.log('粘贴行:', params)
  }, [editable])

  // 行编辑开始
  const handleStartRowEditing = useCallback((event: any) => {
    console.log('开始行编辑:', event)
    dispatchAction('UPDATE')
  }, [dispatchAction])

  // 行编辑结束
  const handleStopRowEditing = useCallback((event: any) => {
    console.log('结束行编辑:', event)
    
    if (event.data) {
      // 如果是新行，添加到插入列表
      if (event.data._isNewRow) {
        editingStateRef.current.insertedRows.push(event.data)
      } else {
        // 否则添加到更新列表
        editingStateRef.current.updatedRows.push(event.data)
      }
      
      setCanSave(true)
      setEnabledActions(['save', 'cancel'])
    }
  }, [])

  return {
    // 编辑状态
    canSave,
    enabledActions,
    
    // 行编辑操作
    handleAddRow,
    handleDeleteRowConfirm,
    handleConfirmModify,
    handleCancelModify,
    handleSave,
    
    // 行数据操作
    handleCloneRow,
    handleResInsert,
    handleResUpdate,
    handleResDelete,
    handlePasteRow,
    
    // 行编辑事件
    handleStartRowEditing,
    handleStopRowEditing,
    
    // 状态检查
    isCellEditing
  }
}