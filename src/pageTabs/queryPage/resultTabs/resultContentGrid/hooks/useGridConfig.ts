import { useMemo, useRef, useCallback } from 'react'
import type { Grid<PERSON>pi, ColumnApi, GridOptions, ColDef } from '@ag-grid-community/core'
import { useSelector } from 'src/hook'
import { indexColId } from 'src/components'
import { GridConfigBase, defaultColDef, infiniteModeOptions } from '../gridConfig/GridConfig'

interface UseGridConfigProps {
  columnInfos: any[]
  theme: string
  scale?: number
  searchHighlightRefs?: {
    matchedCellsSetRef: React.MutableRefObject<Set<string>>
    currentFocusCellKeyRef: React.MutableRefObject<string | null>
  }
  rowNum: number
  editable: boolean
  canCopy: boolean
}

interface UseGridConfigReturn {
  // Grid 配置
  gridConfig: GridOptions
  columnDefs: ColDef[]
  defaultColDef: ColDef
  
  // 无限滚动配置
  infiniteModeOptions: {
    rowModelType: 'infinite'
    cacheBlockSize: number
    maxConcurrentDatasourceRequests: number
    infiniteInitialRowCount: number
    maxBlocksInCache: number
  }
  
  // 数据源
  datasource: any
  setDatasource: (datasource: any) => void
  
  // Grid API 引用
  gridApiRef: React.MutableRefObject<{api: GridApi, columnApi: ColumnApi} | null>
  
  // 列宽自适应
  autoSizeColumns: () => void
}

export function useGridConfig({
  columnInfos,
  theme,
  scale = 100,
  searchHighlightRefs,
  rowNum,
  editable,
  canCopy
}: UseGridConfigProps): UseGridConfigReturn {
  
  // Grid API 引用
  const gridApiRef = useRef<{api: GridApi, columnApi: ColumnApi} | null>(null)
  const datasourceRef = useRef<any>(null)
  
  // 获取全局状态
  const { theme: globalTheme } = useSelector((state: any) => state.app)
  
  // 默认列定义
  const defaultColDef = useMemo<ColDef>(() => ({
    sortable: true,
    filter: true,
    resizable: true,
    suppressMovable: false,
    minWidth: 50,
    // 添加搜索高亮样式
    cellClassRules: searchHighlightRefs ? {
      'search-match': (params) => {
        const cellKey = `${params.node?.id}_${params.colDef?.field}`
        return searchHighlightRefs.matchedCellsSetRef.current.has(cellKey)
      },
      'search-current': (params) => {
        const cellKey = `${params.node?.id}_${params.colDef?.field}`
        return searchHighlightRefs.currentFocusCellKeyRef.current === cellKey
      }
    } : undefined
  }), [searchHighlightRefs])

  // 列定义
  const columnDefs = useMemo(() => {
    if (!columnInfos || columnInfos.length === 0) {
      return []
    }

    return columnInfos.map((col, index) => {
      const colDef: ColDef = {
        field: col.field || col.columnName,
        headerName: col.headerName || col.columnName,
        width: col.width || 150,
        type: col.type,
        editable: editable && col.editable !== false,
        cellRenderer: col.cellRenderer,
        cellEditor: col.cellEditor,
        valueGetter: col.valueGetter,
        valueSetter: col.valueSetter,
        // 继承默认的 cellClassRules
        ...defaultColDef,
        // 合并列特定的样式规则
        cellClassRules: {
          ...defaultColDef.cellClassRules,
          ...(col.cellClassRules || {})
        }
      }

      // 添加序号列特殊处理
      if (col.field === indexColId) {
        colDef.pinned = 'left'
        colDef.width = 60
        colDef.suppressMenu = true
        colDef.sortable = false
        colDef.filter = false
        colDef.editable = false
      }

      return colDef
    })
  }, [columnInfos, editable, defaultColDef])

  // 无限滚动配置
  const infiniteModeOptions = useMemo(() => ({
    rowModelType: 'infinite' as const,
    cacheBlockSize: rowNum,
    maxConcurrentDatasourceRequests: 1,
    infiniteInitialRowCount: rowNum,
    maxBlocksInCache: 5
  }), [rowNum])

  // 获取语言配置
  const { locales } = useSelector((state) => state.login)
  
  // 基础 Grid 配置
  const gridConfig = useMemo<GridOptions>(() => ({
    ...GridConfigBase(locales),
    columnDefs,
    enableRangeSelection: true,
    enableRangeHandle: true,
    suppressRowClickSelection: false,
    rowSelection: 'multiple',
    suppressCopyRowsToClipboard: !canCopy,
    suppressCopySingleCellRanges: !canCopy,
    // 主题相关
    theme: theme || globalTheme,
    // 缩放相关
    headerHeight: Math.round(35 * scale / 100),
    rowHeight: Math.round(35 * scale / 100),
  }), [
    locales,
    columnDefs, 
    canCopy, 
    theme, 
    globalTheme, 
    scale
  ])

  // 设置数据源
  const setDatasource = useCallback((datasource: any) => {
    datasourceRef.current = datasource
    if (gridApiRef.current?.api) {
      gridApiRef.current.api.setDatasource(datasource)
    }
  }, [])

  // 列宽自适应
  const autoSizeColumns = useCallback(() => {
    if (!gridApiRef.current) return
    
    const { api, columnApi } = gridApiRef.current
    
    // 获取所有可见列（排除序号列）
    const colIds = columnApi.getAllColumns()
      ?.filter((col: any) => col.getColId() !== indexColId)
      ?.map((col: any) => col.getColId())
    
    if (colIds?.length && api.getDisplayedRowCount() > 0) {
      // 先执行自动调整
      columnApi.autoSizeColumns(colIds, false)
      
      // 然后限制最大宽度为300px
      colIds.forEach(id => {
        const column = columnApi.getColumn(id)
        const currentWidth = column?.getActualWidth()
        if (currentWidth && currentWidth > 300) {
          columnApi.setColumnWidth(id, 300)
        }
      })
    }
  }, [])

  return {
    // Grid 配置
    gridConfig,
    columnDefs,
    defaultColDef,
    
    // 无限滚动配置
    infiniteModeOptions,
    
    // 数据源
    datasource: datasourceRef.current,
    setDatasource,
    
    // Grid API 引用
    gridApiRef,
    
    // 列宽自适应
    autoSizeColumns
  }
}