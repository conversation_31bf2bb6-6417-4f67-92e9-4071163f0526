import { useState, useRef, useCallback, useEffect, useMemo } from 'react'
import { message } from 'antd'
import { useTranslation } from 'react-i18next'
import { cloneDeep } from 'lodash'
import type { IGetRowsParams, GridApi, ColumnApi } from '@ag-grid-community/core'
import type { IGridContext } from 'src/components'
import { formatFilterModel, getExpandedCurResultData } from '../util'
import { isEmpty } from 'lodash'
import {
  executeSqlSegment,
  explainSqlStatement,
  getSegmentResults,
  type QueryResult,
  type QueryParams
} from 'src/api'
import { useDispatch } from 'src/hook'
import { setTabExecutionStatus, setTabExecutionStatusPercentage } from 'src/pageTabs/queryPage/queryTabs/queryTabsSlice'

interface PaginationState {
  pageNumber: number
  pageSize: number
  total: number | null
  maxNumber: number | null
}

interface UseGridDataProps {
  gridApiRef: React.MutableRefObject<{
    api: GridApi
    columnApi: ColumnApi
  } | null>
  connectionId: string
  dataSourceType: string
  operatingObject: string
  databaseName: string
  statement: string
  queryKey: string
  columnDefs: any[]
  rowNum: number
  isDesensitizedPlaintext: boolean
  detailedResultData: any[]
  currentRusultData: any[]
  dispatchAction: (action: string) => void
  retriggerSearchAfterPagination: () => void
  // 新增的必需属性
  result: any // 初始结果数据
  plSql?: boolean
  txMode?: string
  actionType?: string
  cursorInfo?: any
  type?: string
  currentParams?: any
}

interface UseGridDataReturn {
  // 分页状态
  curPagination: PaginationState | null
  setCurPagination: React.Dispatch<React.SetStateAction<PaginationState | null>>
  hasNextPage: boolean
  setHasNextPage: React.Dispatch<React.SetStateAction<boolean>>
  
  // 数据状态
  loading: boolean
  setLoading: React.Dispatch<React.SetStateAction<boolean>>
  pageTotal: number
  setPageTotal: React.Dispatch<React.SetStateAction<number>>
  
  // 缓存数据引用
  cachePageResultDataRef: React.MutableRefObject<any[]>
  notSupportPageResultDataRef: React.MutableRefObject<any[]>
  curCallbackRef: React.MutableRefObject<((data: any[], lastRow?: number) => void) | null>
  
  // 核心数据获取方法
  commonGetRows: (params: IGetRowsParams & {page?: number}, isPaginationAction?: boolean) => Promise<void>
  loadPage: (page: number, pageSize?: number, sortModel?: any, filterModel?: any) => void
  
  // 分页处理方法
  handlePageChange: (newPageNumber: number) => void
  handlePageSizeChange: (newPageSize: number) => void
  handleRefresh: (keepFilter?: boolean) => void
  
  // 计算的分页数据
  paginationData: {
    currentPageNumber: number
    pageSize: number
    startRowInPage: number
    endRowInPage: number
    hasNextPage: boolean
    isLoading: boolean
    total: number | null
  }
  
  // 数据处理方法
  handleRowDataWithEditable: (data: any[]) => any[]
  getLastRow: (data: any[], startRow: number) => number
}

export function useGridData({
  gridApiRef,
  connectionId,
  dataSourceType,
  operatingObject,
  databaseName,
  statement,
  queryKey,
  columnDefs,
  rowNum,
  isDesensitizedPlaintext,
  detailedResultData,
  currentRusultData,
  dispatchAction,
  retriggerSearchAfterPagination,
  result,
  plSql = false,
  txMode = 'auto',
  actionType = 'ROLLING_RESULT',
  cursorInfo,
  type = 'execute',
  currentParams
}: UseGridDataProps): UseGridDataReturn {

  const { t } = useTranslation()
  const dispatch = useDispatch()

  // 分页状态
  const [curPagination, setCurPagination] = useState<PaginationState | null>(null)
  const [hasNextPage, setHasNextPage] = useState(false)
  const [loading, setLoading] = useState(false)
  const [pageTotal, setPageTotal] = useState(0)

  // 缓存数据引用
  const cachePageResultDataRef = useRef<any[]>([])
  const notSupportPageResultDataRef = useRef<any[]>([])
  const curCallbackRef = useRef<((data: any[], lastRow?: number) => void) | null>(null)
  const isInitializeDataUsed = useRef<boolean>(false)

  // 处理查询到的结果
  const formatExecuteStatementResult = useCallback((data: any) => {
    const { executionInfos } = data?.messageData || {};
    let queryResult: QueryResult[] = [];
    executionInfos.forEach((item: any) => {
      queryResult.push(item.response);
    });
    return queryResult;
  }, [])

  // 使用 useCallback 稳定 recursiveQuery 函数引用
  const recursiveQuery = useCallback(async (params: QueryParams, delay = 100): Promise<QueryResult[]> => {
    try {
      const data = await getSegmentResults(params);
      if (data?.messageData) {
        return new Promise((resolve) => {
          setTimeout(async () => {
            const max = 3000;
            let nextDelay = delay;
            if (delay < max) {
              nextDelay = delay + 100;
            }
            const result = await recursiveQuery(params, nextDelay);
            resolve(result);
          }, delay);
        });
      }
      return formatExecuteStatementResult(data);
    } catch (error) {
      console.error('轮询出错：', error);
      throw error;
    }
  }, [formatExecuteStatementResult]);

  // 获取块数据的函数
  const fetchBlockDataFunction = useCallback(async (
    offset: number = 0,
    sortModels: any,
    filterModel: any,
    rowCount: undefined | number = (curPagination?.pageSize || rowNum) + 1, // N+1分页：请求多一条数据
  ) => {
    // 首块数据，第一次获取时使用初始化数据。第二次获取使用远程数据
    if (offset === 0 && !isInitializeDataUsed.current) {
      isInitializeDataUsed.current = true
      return Promise.resolve([cloneDeep(result)])
    }

    const payload = {
      connectionId,
      dataSourceType,
      databaseName,
      operatingObject,
      statements: [statement],
      offset,
      rowCount,
      tabKey: queryKey,
      plSql,
      sortModels,
      filterModel,
      autoCommit: txMode === 'auto',
      actionType: actionType,
      // 如果存在 cursorInfo，添加到 payload 中
      ...(cursorInfo ? { cursorInfo } : {})
    }

    // 设置执行 pending
    dispatch(setTabExecutionStatus({ key: queryKey, pending: true }))
    dispatch(setTabExecutionStatusPercentage({ key: queryKey, executePercentage: 0, executeStatusMessage: `${t('sdo_executing_status')}...0%` }));

    if (type === 'explain') {
      const res = explainSqlStatement(isDesensitizedPlaintext ? currentParams : payload)
      // 恢复面板为可执行状态
      dispatch(setTabExecutionStatus({ key: queryKey, pending: false }))
      dispatch(setTabExecutionStatusPercentage({ key: queryKey, executePercentage: 100, executeStatusMessage: `${t('sdo_execution_completed')}` }));
      return res
    } else {
      const res = await executeSqlSegment(isDesensitizedPlaintext ? currentParams : payload)
      const { channelId, groupName, messageId, messageData = [], executeStatus } = res;

      //不为pendding即为执行结束
      if (executeStatus !== 'PENDDING' && messageData) {
       return formatExecuteStatementResult(res)
      }
      const params = { channelId, groupName, messageId }
      const result = await recursiveQuery(params);
      return result
    }
  }, [
    connectionId, dataSourceType, databaseName, operatingObject, statement, queryKey,
    plSql, txMode, actionType, cursorInfo, type, isDesensitizedPlaintext, currentParams,
    result, dispatch, t, formatExecuteStatementResult, recursiveQuery, curPagination?.pageSize, rowNum
  ])

  // 数据处理方法
  const handleRowDataWithEditable = useCallback((data: any[]) => {
    return data.map((curItem: any) => {
      const customCurItem = Object.assign({ editable: true }, curItem)
      const keys = Object.keys(curItem)
  
      for (const key of keys) {
        const value = curItem[key]

        // 如果包含 cursorValue，保留整个对象；否则只保留 value 字段
        if (value?.cursorValue !== undefined) {
          customCurItem[key] = value // 保留完整对象以便访问 cursorValue
        } else {
          customCurItem[key] = value?.value
        }

        if (value?.formatValue) {
          customCurItem[`get${key}`] = () => value.formatValue
        }

        if (value?.renderType?.includes('binary') && value?.size) {
          customCurItem[`${key}CQSize`] = value.size
        }

        if (!value?.editable) {
          customCurItem.editable = false
        }
      }

      return customCurItem
    })
  }, [])

  const getLastRow = useCallback((data: any[], startRow: number) => {
    return data?.length + startRow
  }, [])

  // 通用getRows
  const commonGetRows = useCallback(async (
    params: IGetRowsParams & {page?: number}, 
    isPaginationAction: boolean = false
  ) => {
    // 在数据量大或其他情况导致获取结果慢的情况下，不允许有其他获取结果集的操作
    const {
      startRow,
      endRow,
      sortModel: sortInfo,
      filterModel,
      successCallback,
      failCallback,
      context,
      page = 1
    } = params

    console.log('getRows')
    const { status } = context as IGridContext
    //每次分页都需要更新
    curCallbackRef.current = successCallback
  
    // 编辑后没保存数据时直接return (当startRow % rowNum === 0 时，是分页的接口请求会触发getRows)
    if (['CLONE', 'INSERT', 'UPDATE'].includes(status)) {
      // 空列表插入数据时会触发getRows，此时status为INSERT，startRow为0，直接return
      if(status === 'INSERT' && cachePageResultDataRef?.current?.length === 1) {
        return
      }
      failCallback()
      return message.error(t('sdo_save_first'))
    }

    setLoading(true)

    try {
      // 不支持分页不需要重新请求数据
      if(notSupportPageResultDataRef.current?.length){
        const resultData = notSupportPageResultDataRef.current                                                                                
        const curResultData = resultData.slice(startRow, endRow)  
        cachePageResultDataRef.current = curResultData

        let totalRows = resultData.length
        setPageTotal(totalRows)
        successCallback(curResultData, curResultData.length)
        return
      } else if (isDesensitizedPlaintext && startRow === 0) {
        let totalRows = detailedResultData?.length
        setPageTotal(totalRows)
        successCallback(currentRusultData, getLastRow(currentRusultData, startRow))
        return
      }

      /* 0 格式化过滤参数 */
      const formattedFilterModel = formatFilterModel(filterModel)
      /* 0 处理排序数据 应后端要求增加一个index列标值*/
      let sortModel: any[] = sortInfo
      if(!isEmpty(sortInfo)) {
        sortModel = sortInfo.map((item: any) => {
          return {
            ...item,
            index: columnDefs.findIndex((col) => col.field === item.colId)
          }
        })
      }
      
      /* 1 获取块数据 */
      let response = await fetchBlockDataFunction(
        startRow,
        sortModel,
        formattedFilterModel
      )
      
      let [{ resultData: defaultResultData, detailedResultData: defaultDetailResultData, executeError, supportPage,
       databaseName: dbName, connectionId: connId, dataSourceType: dsType, operatingObject: opObj, statementObject: tableName
      }] = response || []
      
      /* 2 错误处理 */
      if (executeError) {
        console.error(executeError)
      }
      
      // 增加editable字段，用于判断是否可以删除
      let resultData = handleRowDataWithEditable(defaultDetailResultData)
      //oracle自定义字段
      if (dataSourceType === 'Oracle') {
        resultData = getExpandedCurResultData(defaultDetailResultData)
      } 

      // N+1分页逻辑：判断是否有下一页
      let actualResultData = resultData
      let hasNext = false
      let calculatedTotal: number | null = null // 默认不设置总数
      
      if (resultData?.length === (curPagination?.pageSize || rowNum) + 1) {
        // 返回了 pageSize + 1 条数据，说明有下一页
        hasNext = true
        actualResultData = resultData.slice(0, curPagination?.pageSize || rowNum) // 只显示前 pageSize 条
        // 有下一页时，不设置总数，让分页组件继续使用 N+1 逻辑
        calculatedTotal = null
      } else {
        // 返回的数据 <= pageSize，说明这是最后一页
        hasNext = false
        actualResultData = resultData
        // 当到达最后一页时，计算并设置准确的总数
        calculatedTotal = actualResultData.length + startRow
      }
      
      setHasNextPage(hasNext)
      setPageTotal(actualResultData.length + startRow)

      cachePageResultDataRef.current = actualResultData
      //返回实际显示的数据
      console.log(actualResultData, actualResultData?.length)
      successCallback(actualResultData, actualResultData?.length)
      
      // 更新分页状态，确保总数被正确设置
      setCurPagination(prev => ({
        pageNumber: page,
        pageSize: prev?.pageSize || rowNum,
        total: calculatedTotal, // 直接使用 calculatedTotal，有下一页时为 null，最后一页时为准确总数
        maxNumber: prev?.maxNumber || null
      }))
     
    } catch (error) {
      failCallback()
      console.log(error)
    } finally {
      console.log('final')
      setLoading(false)
      dispatchAction('reset')
    }
  }, [
    connectionId, dataSourceType, operatingObject, databaseName, statement, queryKey,
    columnDefs, rowNum, isDesensitizedPlaintext, detailedResultData, currentRusultData,
    curPagination?.pageSize, t, handleRowDataWithEditable, getLastRow, dispatchAction
  ])

  const loadPage = useCallback((page: number, pageSize?: number, sortModel?: any, filterModel?: any) => {
    const curPageSize = pageSize || curPagination?.pageSize || rowNum
    const startRow = (page - 1) * curPageSize
    const endRow = startRow + curPageSize

    gridApiRef?.current?.api?.setDatasource({
      getRows: async (params) => {
        const finalParams = {
          ...params,
          startRow,
          endRow,
          page,
          // 优先使用保存的过滤和排序状态，如果没有则使用 AG-Grid 的默认值
          sortModel: sortModel || params.sortModel || [],
          filterModel: filterModel || params.filterModel || {}
        }
        commonGetRows(finalParams, true)
      }
    })
  }, [curPagination?.pageSize, rowNum, gridApiRef, commonGetRows])

  // 处理页码变化
  const handlePageChange = useCallback((newPageNumber: number) => {
    // 检查是否有单元格正在编辑
    const editingCells = gridApiRef.current?.api?.getEditingCells()
    if (editingCells && editingCells.length > 0) {
      return message.warn(t('sdo_complete_current_edit'))
    }

    dispatchAction('reset')

    // 更新分页状态，保持已知的总数
    setCurPagination(prev => ({
      pageNumber: newPageNumber,
      pageSize: prev?.pageSize || rowNum,
      total: prev?.total || null, // 保持现有的 total 值，不要重置
      maxNumber: prev?.maxNumber || null,
    }))

    // 如果已知总数，立即更新 hasNextPage 状态
    if (curPagination?.total !== null && curPagination?.total !== undefined && curPagination?.total > 0) {
      const pageSize = curPagination?.pageSize || rowNum
      const totalPages = Math.ceil(curPagination.total / pageSize)
      setHasNextPage(newPageNumber < totalPages)
    }

    // 分页后重新触发搜索
    retriggerSearchAfterPagination()
  }, [curPagination, dispatchAction, t, retriggerSearchAfterPagination, rowNum, gridApiRef])

  // 处理页容量变化
  const handlePageSizeChange = useCallback((newPageSize: number) => {
    // 保持已知的总数，即使改变页面大小
    const currentTotal = curPagination?.total || null
    setCurPagination(prev => ({
      pageNumber: 1, // 重置到第一页
      pageSize: newPageSize,
      total: currentTotal, // 保持总数
      maxNumber: prev?.maxNumber || null,
    }))
    retriggerSearchAfterPagination()
  }, [curPagination, retriggerSearchAfterPagination])

  // 刷新处理
  const handleRefresh = useCallback((keepFilter: boolean = false) => {
    if (!keepFilter) {
      dispatchAction('reset')
    }
    
    // 重新加载当前页
    loadPage(curPagination?.pageNumber || 1, curPagination?.pageSize)
  }, [curPagination, dispatchAction, loadPage])

  // 计算分页数据
  const paginationData = useMemo(() => {
    const currentPage = curPagination?.pageNumber || 1
    const pageSize = curPagination?.pageSize || rowNum
    const startRow = (currentPage - 1) * pageSize + 1
    const endRow = startRow + (cachePageResultDataRef.current?.length || 0) - 1
    
    // 优化 hasNextPage 的判断逻辑：优先使用已知的总数计算
    let calculatedHasNextPage = hasNextPage
    if (curPagination?.total !== null && curPagination?.total !== undefined && curPagination?.total > 0) {
      const totalPages = Math.ceil(curPagination.total / pageSize)
      calculatedHasNextPage = currentPage < totalPages
    } else {
      // 当没有总数时，使用 N+1 逻辑：如果当前页数据量等于页面大小，则可能有下一页
      // 除非明确知道没有下一页（通过 hasNextPage 状态）
      const currentPageDataLength = cachePageResultDataRef.current?.length || 0
      if (currentPageDataLength === pageSize) {
        calculatedHasNextPage = hasNextPage // 依赖从接口返回的 hasNextPage 状态
      } else {
        calculatedHasNextPage = false // 当前页数据不足页面大小，肯定没有下一页
      }
    }
    
    return {
      currentPageNumber: currentPage,
      pageSize,
      startRowInPage: startRow,
      endRowInPage: Math.max(startRow, endRow),
      hasNextPage: calculatedHasNextPage,
      isLoading: loading,
      total: curPagination?.total ?? null // 传递总数给分页组件，undefined 转为 null
    }
  }, [curPagination?.pageNumber, curPagination?.pageSize, curPagination?.total, rowNum, hasNextPage, loading, cachePageResultDataRef.current?.length])

  return {
    // 分页状态
    curPagination,
    setCurPagination,
    hasNextPage,
    setHasNextPage,
    
    // 数据状态
    loading,
    setLoading,
    pageTotal,
    setPageTotal,
    
    // 缓存数据引用
    cachePageResultDataRef,
    notSupportPageResultDataRef,
    curCallbackRef,
    
    // 核心数据获取方法
    commonGetRows,
    loadPage,
    
    // 分页处理方法
    handlePageChange,
    handlePageSizeChange,
    handleRefresh,
    
    // 计算的分页数据
    paginationData,
    
    // 数据处理方法
    handleRowDataWithEditable,
    getLastRow
  }
}