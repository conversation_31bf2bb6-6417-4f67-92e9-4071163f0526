import { useCallback, useState } from 'react'
import type { <PERSON>rid<PERSON><PERSON>, <PERSON>umn<PERSON><PERSON>, CellKeyDownEvent, CellClickedEvent, CellFocusedEvent } from '@ag-grid-community/core'
import { indexColId } from 'src/components'

interface UseGridKeyboardHandlersProps {
  gridApiRef: React.MutableRefObject<{
    api: GridApi
    columnApi: ColumnApi
  } | null>
  setVisibleCellViewer: React.Dispatch<React.SetStateAction<boolean>>
  setVisibleRowViewer: React.Dispatch<React.SetStateAction<boolean>>
  setFocusedColumn: React.Dispatch<React.SetStateAction<any>>
  setFocusedRowIndex: React.Dispatch<React.SetStateAction<number | null>>
  setFocusedRowData: React.Dispatch<React.SetStateAction<any>>
}

interface UseGridKeyboardHandlersReturn {
  // 焦点状态
  focusedColumn: any
  focusedRowIndex: number | null
  focusedRowData: any
  
  // 事件处理器
  onCellKeyDown: (event: CellKeyDownEvent) => void
  handleCellClicked: (params: CellClickedEvent) => void
  handleCellFocused: (event: CellFocusedEvent) => void
  handleToolbarView: () => void
  
  // 行导航
  lastRowIndex: () => void
  nextRowIndex: () => void
}

export function useGridKeyboardHandlers({
  gridApiRef,
  setVisibleCellViewer,
  setVisibleRowViewer,
  setFocusedColumn,
  setFocusedRowIndex,
  setFocusedRowData
}: UseGridKeyboardHandlersProps): UseGridKeyboardHandlersReturn {
  
  // 焦点状态
  const [focusedColumn, setFocusedColumnState] = useState<any>(null)
  const [focusedRowIndex, setFocusedRowIndexState] = useState<number | null>(null)
  const [focusedRowData, setFocusedRowDataState] = useState<any>({})

  // 更新焦点列
  const updateFocusedColumn = useCallback((column: any) => {
    setFocusedColumn(column)
    setFocusedColumnState(column)
  }, [setFocusedColumn])

  // 更新焦点行索引
  const updateFocusedRowIndex = useCallback((index: number | null) => {
    setFocusedRowIndex(index)
    setFocusedRowIndexState(index)
  }, [setFocusedRowIndex])

  // 更新焦点行数据
  const updateFocusedRowData = useCallback((data: any) => {
    setFocusedRowData(data)
    setFocusedRowDataState(data)
  }, [setFocusedRowData])

  // 单元格键盘事件处理
  const onCellKeyDown = useCallback((event: CellKeyDownEvent) => {
    const { event: keyboardEvent, column, rowIndex, data } = event

    if (!keyboardEvent || !column) return

    // 将通用 Event 类型转换为 KeyboardEvent 类型
    const typedKeyboardEvent = keyboardEvent as unknown as KeyboardEvent

    // 更新焦点信息
    updateFocusedColumn(column)
    updateFocusedRowIndex(rowIndex)
    updateFocusedRowData(data)

    // 处理特殊按键
    switch (typedKeyboardEvent.key) {
      case 'Enter':
        // Enter键 - 查看单元格详情
        if (typedKeyboardEvent.shiftKey) {
          // Shift+Enter - 查看行详情
          handleToolbarView()
        } else {
          // Enter - 查看单元格详情
          if (column.getColId() === indexColId) {
            setVisibleRowViewer(true)
          } else {
            setVisibleCellViewer(true)
          }
        }
        typedKeyboardEvent.preventDefault()
        break

      case 'F2':
        // F2键 - 进入编辑模式
        if (gridApiRef.current?.api && rowIndex !== null) {
          gridApiRef.current.api.startEditingCell({
            rowIndex,
            colKey: column.getColId()
          })
        }
        typedKeyboardEvent.preventDefault()
        break

      case 'Escape':
        // ESC键 - 停止编辑
        if (gridApiRef.current?.api) {
          gridApiRef.current.api.stopEditing(true) // true表示取消编辑
        }
        typedKeyboardEvent.preventDefault()
        break
        
      case 'ArrowUp':
        // 向上箭头
        if (typedKeyboardEvent.ctrlKey) {
          // Ctrl+向上 - 跳到第一行
          if (gridApiRef.current?.api) {
            gridApiRef.current.api.ensureIndexVisible(0)
            gridApiRef.current.api.setFocusedCell(0, column.getColId())
          }
          typedKeyboardEvent.preventDefault()
        }
        break

      case 'ArrowDown':
        // 向下箭头
        if (typedKeyboardEvent.ctrlKey) {
          // Ctrl+向下 - 跳到最后一行
          if (gridApiRef.current?.api) {
            const lastRowIndex = gridApiRef.current.api.getDisplayedRowCount() - 1
            gridApiRef.current.api.ensureIndexVisible(lastRowIndex)
            gridApiRef.current.api.setFocusedCell(lastRowIndex, column.getColId())
          }
          typedKeyboardEvent.preventDefault()
        }
        break
        
      case 'Home':
        // Home键 - 跳到行首
        if (gridApiRef.current?.columnApi && rowIndex !== null) {
          const firstColumn = gridApiRef.current.columnApi.getAllDisplayedColumns()[0]
          if (firstColumn) {
            gridApiRef.current.api.setFocusedCell(rowIndex, firstColumn.getColId())
          }
        }
        typedKeyboardEvent.preventDefault()
        break

      case 'End':
        // End键 - 跳到行尾
        if (gridApiRef.current?.columnApi && rowIndex !== null) {
          const columns = gridApiRef.current.columnApi.getAllDisplayedColumns()
          const lastColumn = columns[columns.length - 1]
          if (lastColumn) {
            gridApiRef.current.api.setFocusedCell(rowIndex, lastColumn.getColId())
          }
        }
        typedKeyboardEvent.preventDefault()
        break
    }
  }, [gridApiRef, updateFocusedColumn, updateFocusedRowIndex, updateFocusedRowData, setVisibleCellViewer, setVisibleRowViewer])

  // 单元格点击事件处理
  const handleCellClicked = useCallback((params: CellClickedEvent) => {
    const { column, rowIndex, data } = params
    
    if (!column) return

    // 更新焦点信息
    updateFocusedColumn(column)
    updateFocusedRowIndex(rowIndex)
    updateFocusedRowData(data)
  }, [updateFocusedColumn, updateFocusedRowIndex, updateFocusedRowData])

  // 单元格焦点事件处理
  const handleCellFocused = useCallback((event: CellFocusedEvent) => {
    const { column, rowIndex, api } = event
    
    if (!column || rowIndex === null) return

    // 获取行数据
    const rowNode = api.getDisplayedRowAtIndex(rowIndex)
    const data = rowNode?.data || {}

    // 更新焦点信息
    updateFocusedColumn(column)
    updateFocusedRowIndex(rowIndex)
    updateFocusedRowData(data)
  }, [updateFocusedColumn, updateFocusedRowIndex, updateFocusedRowData])

  // 工具栏查看处理
  const handleToolbarView = useCallback(() => {
    if (!focusedColumn) return
    
    const colId = focusedColumn.getColId()
    if (colId === indexColId) {
      // 是序号列，展示行数据
      setVisibleRowViewer(true)
    } else {
      // 展示单元格数据
      setVisibleCellViewer(true)
    }
  }, [focusedColumn, setVisibleRowViewer, setVisibleCellViewer])

  // 行导航 - 上一行
  const lastRowIndex = useCallback(() => {
    updateFocusedRowIndex((index: number | null) => {
      return index ? index - 1 : index
    })
  }, [updateFocusedRowIndex])

  // 行导航 - 下一行
  const nextRowIndex = useCallback(() => {
    updateFocusedRowIndex((index: number | null) => {
      return (index || index === 0) ? index + 1 : index
    })
  }, [updateFocusedRowIndex])

  return {
    // 焦点状态
    focusedColumn,
    focusedRowIndex,
    focusedRowData,
    
    // 事件处理器
    onCellKeyDown,
    handleCellClicked,
    handleCellFocused,
    handleToolbarView,
    
    // 行导航
    lastRowIndex,
    nextRowIndex
  }
}