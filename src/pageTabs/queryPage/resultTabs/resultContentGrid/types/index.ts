import type { PaneType } from '../../../queryTabs/queryTabsSlice'
import type { DataSourceType, QueryBase } from 'src/api'

// 主组件 Props 接口
export interface ResultGridPaginationProps {
  // 结果数据
  result?: {
    connectionId: string
    dataSourceType: string
    databaseName: string
    operatingObject: string
    statement: string
    containTempTable?: boolean
    tabKey: string
    connectionType?: string
  }
  
  // 列信息
  columnInfos?: any[]
  
  // 主题和样式
  theme?: string
  scale?: number
  
  // 分页配置
  rowNum?: number
  
  // 权限和功能配置
  editable?: boolean
  canCopy?: boolean
  refreshable?: boolean
  paneType?: PaneType
  type?: string
  isExplain?: boolean
  
  // 权限结果
  permissionResult?: {
    resultCellCopy?: boolean
    resultSetBinaryFileDownload?: boolean
    supportResultSetSqlGenerator?: boolean
    resultSetSqlGenerator?: boolean
    allNodePathString?: string[]
  }
  
  // 导出配置
  dataExport?: {
    status?: boolean
    showExported?: boolean
  }
  
  // 过滤和脱敏
  filterNames?: any[]
  txMode?: 'auto' | 'manual'
  isDesensitized?: boolean
  maskedPathMap?: {[key: string]: boolean}
  
  // 标签页信息
  tabInfoMap?: {[key: string]: any}
  activeTabKey?: string
  
  // 其他配置
  doubleCheckType?: string
  isDesensitizedPlaintext?: boolean
  detailedResultData?: any[]
  currentRusultData?: any[]
  copySetting?: boolean
}

// 分页状态接口
export interface PaginationState {
  pageNumber: number
  pageSize: number
  total?: number | null
  maxNumber?: number | null
}

// 网格操作状态接口
export interface GridOperationState {
  canSave: boolean
  enabledActions: string[]
  insertedRows: any[]
  updatedRows: any[]
  deletedRows: any[]
}

// 搜索状态接口
export interface SearchState {
  visible: boolean
  currentText: string
  totalMatches: number
  currentMatchIndex: number
  allMatchLocations: any[]
}

// 导出配置接口
export interface ExportConfig {
  format?: 'csv' | 'excel' | 'json' | 'sql'
  fileName?: string
  includeHeader?: boolean
  maxRows?: number
}

// 选择上下文接口
export interface SelectionContext {
  selectedCells: any[]
  selectedRows: any[]
  selectedColumns: any[]
  hasSelection: boolean
}

// 网格上下文接口
export interface GridContextProps {
  canSave: boolean
  status: string
  disableContextMenu: boolean
  copyable: boolean
  canCopyCell: boolean
  allowClone: boolean
  allowCreateSql: boolean
  createSqlDisabled: boolean
  queryKey: string
  selectionContext?: SelectionContext
}

// 事件处理器接口
export interface GridEventHandlers {
  onGridReady: (event: any) => void
  onCellFocused: (event: any) => void
  onCellKeyDown: (event: any) => void
  onCellClicked: (params: any) => void
  onCellContextMenu: (params: any) => void
  onRowEditingStarted: (event: any) => void
  onRowEditingStopped: (event: any) => void
  onSelectionChanged: (event: any) => void
  onSortChanged: () => void
  onFilterChanged: () => void
}

// Hook 返回值类型
export interface UseGridDataReturn {
  loading: boolean
  paginationData: {
    currentPageNumber: number
    pageSize: number
    startRowInPage: number
    endRowInPage: number
    hasNextPage: boolean
    isLoading: boolean
    total: number | null
  }
  curPagination: PaginationState | null
  setCurPagination: React.Dispatch<React.SetStateAction<PaginationState | null>>
  hasNextPage: boolean
  setHasNextPage: React.Dispatch<React.SetStateAction<boolean>>
  cachePageResultDataRef: React.MutableRefObject<any[]>
  commonGetRows: (params: any, isPaginationAction?: boolean) => Promise<void>
  loadPage: (page: number, pageSize?: number, sortModel?: any, filterModel?: any) => void
  handlePageChange: (newPageNumber: number) => void
  handlePageSizeChange: (newPageSize: number) => void
  handleRefresh: (keepFilter?: boolean) => void
}

export interface UseGridOperationsReturn {
  canSave: boolean
  enabledActions: string[]
  handleAddRow: () => void
  handleDeleteRowConfirm: () => void
  handleConfirmModify: () => Promise<void>
  handleCancelModify: () => void
  handleSave: () => Promise<void>
  handleCloneRow: (params: any) => void
  handleResInsert: (params: any) => Promise<void>
  handleResUpdate: (params: any) => Promise<void>
  handleResDelete: (params: any) => Promise<void>
  handlePasteRow: (params: any) => void
  handleStartRowEditing: (event: any) => void
  handleStopRowEditing: (event: any) => void
  isCellEditing: () => boolean
}

export interface UseGridConfigReturn {
  gridConfig: any
  columnDefs: any[]
  defaultColDef: any
  infiniteModeOptions: any
  datasource: any
  setDatasource: (datasource: any) => void
  gridApiRef: React.MutableRefObject<any>
  autoSizeColumns: () => void
}

export interface UseExportOperationsReturn {
  visibleCommonExport: boolean
  visibleSelectedExport: boolean
  visibleExportAll: boolean
  setVisibleCommonExport: React.Dispatch<React.SetStateAction<boolean>>
  setVisibleSelectedExport: React.Dispatch<React.SetStateAction<boolean>>
  setVisibleExportAll: React.Dispatch<React.SetStateAction<boolean>>
  isDataExportPermissionEnabled: boolean
  onOpenExportModal: (key: string) => void
  applyDataExportPermission: (exportType?: string) => Promise<void>
  handleExportAllResult: (data: any) => Promise<void>
  exportTaskCreatedNot: () => void
}

export interface UseGridKeyboardHandlersReturn {
  focusedColumn: any
  focusedRowIndex: number | null
  focusedRowData: any
  onCellKeyDown: (event: any) => void
  handleCellClicked: (params: any) => void
  handleCellFocused: (event: any) => void
  handleToolbarView: () => void
  lastRowIndex: () => void
  nextRowIndex: () => void
}