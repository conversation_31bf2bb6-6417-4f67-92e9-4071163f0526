# Result Content Grid - Feature Documentation

## Grid System Architecture

### Refactored Modular Architecture

The Result Content Grid feature has been successfully refactored from a 2865-line monolithic component into a sophisticated 3-layer modular architecture, achieving 90% code reduction in the main component while maintaining full backward compatibility.

**Architecture Overview:**
```
Hook Layer (Business Logic)    │ Service Layer (Data Processing)    │ Component Layer (UI)
├── useGridData.ts (248 lines) │ ├── GridDataService.ts (103 lines) │ ├── GridContainer.tsx (139 lines)
├── useGridOperations.ts (225) │ ├── GridOperationsService.ts (182)  │ ├── GridModals.tsx (151 lines)
├── useGridConfig.ts (124)     │ ├── ExportService.ts (189)         │ └── ResultGridPaginationRefactored.tsx (501)
├── useExportOperations.ts (85)│ ├── ContextMenuHandlers.ts         │
└── useGridKeyboardHandlers.ts │ └── CustomSelectionController.ts   │
```

### Core N+1 Pagination Algorithm

The system implements an innovative N+1 pagination strategy that avoids expensive COUNT queries while providing intelligent total count display:

**Algorithm Implementation:**
```typescript
// Request pageSize + 1 records to determine if next page exists
const actualPageSize = pageSize + 1;
const resultData = await fetchBlockDataOrFromStore(startRow, sortModel, filterModel, {
  pageSize: actualPageSize,
  // ... other params
});

// Smart pagination logic
if (resultData?.length === actualPageSize) {
  hasNext = true;
  actualResultData = resultData.slice(0, pageSize); // Display only pageSize records
  calculatedTotal = null; // Keep total as null for N+1 logic
} else {
  hasNext = false;
  actualResultData = resultData;
  calculatedTotal = actualResultData.length + startRow - 1; // Calculate exact total for last page
}
```

**Total Display Strategy (3-tier priority):**
1. **Exact Count**: When on last page, show precise total
2. **User-Provided**: Use `result.total` from query execution  
3. **N+ Indicator**: Show "N+" when exact count unknown

## Implementation Patterns

### Hook Composition Pattern

The feature uses a sophisticated hook composition pattern where each hook has a single responsibility:

```typescript
const ResultGridPaginationRefactored: React.FC<Props> = (props) => {
  // Data management with N+1 pagination
  const gridData = useGridData({
    gridApiRef, connectionId, dataSourceType, statement,
    rowNum, statementObject, operatingObject, curPagination, setCurPagination
  });
  
  // CRUD operations with state machine
  const operations = useGridOperations({
    gridApiRef, editable, connectionType,
    cachePageResultDataRef: gridData.cachePageResultDataRef,
    handleRefresh: gridData.handleRefresh
  });
  
  // AG-Grid configuration with theme support
  const gridConfig = useGridConfig({
    columnInfos, theme, searchHighlightRefs, result, scalePageState,
    rowNum, paneType, connectionType
  });
};
```

### Multi-Level Caching Strategy

**Three-tier caching system:**
```typescript
// Current page cache for active data
cachePageResultDataRef.current = { [pageKey]: processedData };

// All pages cache for navigation
cacheAllPageResultDataRef.current = { 
  [pageKey]: pageData,
  [filterKey]: pageData 
};

// Non-pagination cache for small result sets
notSupportPageResultDataRef.current = fullResultSet;
```

**Cache Invalidation Rules:**
- Filter/Sort changes: Clear all caches, reset to page 1
- Data operations (CRUD): Clear affected page caches
- Connection changes: Clear all caches

### State Machine Pattern

**Grid Operation States:**
```typescript
type IGridStatus = 'NORMAL' | 'UPDATE' | 'INSERT' | 'DELETE' | 'CLONE'

// State transitions
NORMAL → INSERT/UPDATE/CLONE (enter edit mode)
INSERT/UPDATE/CLONE → NORMAL (save/cancel)
NORMAL → DELETE (bulk delete mode)
```

**Context-Aware Operations:**
```typescript
const agContext: SimpleGridContext = {
  canSave: operations.canSave,
  status: operations.status,
  disableContextMenu: paneType === 'tSql',
  copyable: canCopy,
  allowClone: !(connectionType === 'DB2' || connectionType === 'StarRocks') && editable,
  allowCreateSql: permissionResult?.supportResultSetSqlGenerator || false
};
```

### Error Handling Strategies

**Service Layer Error Handling:**
```typescript
interface OperationResult {
  success: boolean;
  data?: any;
  error?: string;
}

// Consistent error handling pattern
static async insertRow(rowData: any, params: ModifyParams): Promise<OperationResult> {
  try {
    const result = await executeOperation(rowData, params);
    return { success: true, data: result };
  } catch (error) {
    console.error('Insert operation failed:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : '操作失败'
    };
  }
}
```

**Boundary Protection:**
```typescript
// Prevent data loss during editing
const preventDataLoss = () => {
  const editingCells = gridApiRef.current?.api?.getEditingCells();
  if (editingCells?.length > 0) {
    message.warning('请先保存或取消当前编辑');
    return false;
  }
  return true;
};
```

## Key Files and Structure

### Hook Layer (`/hooks/`)
```
├── useGridData.ts (248 lines)
│   ├── N+1 pagination implementation
│   ├── Multi-level caching logic
│   ├── AG-Grid datasource integration
│   └── Page navigation handlers
│
├── useGridOperations.ts (225 lines) 
│   ├── CRUD operation state management
│   ├── Edit mode state machine
│   ├── Toolbar action coordination
│   └── Data validation patterns
│
├── useGridConfig.ts (124 lines)
│   ├── AG-Grid configuration management
│   ├── Column definition processing
│   ├── Theme and locale integration
│   └── Performance optimization settings
│
├── useExportOperations.ts (85 lines)
│   ├── Export modal state management
│   ├── Permission verification
│   ├── Format-specific export handling
│   └── Progress tracking
│
└── useGridKeyboardHandlers.ts (162 lines)
    ├── Keyboard navigation
    ├── Shortcut key handling
    ├── Focus management
    └── Toolbar integration
```

### Service Layer (`/services/`)
```
├── GridDataService.ts (103 lines)
│   ├── fetchBlockDataOrFromStore(): Core data fetching
│   ├── getExpandedCurResultData(): Oracle JSON expansion
│   └── calculatePaginationInfo(): Pagination calculations
│
├── GridOperationsService.ts (182 lines)
│   ├── CRUD operation implementations
│   ├── Data validation and transformation
│   └── Transaction management
│
├── ExportService.ts (189 lines)
│   ├── Multi-format export (CSV, Excel, JSON, SQL)
│   ├── Permission-based export filtering
│   └── Progress tracking and error handling
│
├── ContextMenuHandlers.ts
│   ├── Right-click menu operations
│   ├── Cell-specific actions
│   └── Selection-based operations
│
└── CustomSelectionController.ts
    ├── Row/cell selection logic
    ├── Range selection handling
    └── Selection state synchronization
```

### Component Layer (`/components/`)
```
├── GridContainer.tsx (139 lines)
│   ├── Pure AG-Grid wrapper
│   ├── Event handler binding
│   ├── Performance optimization
│   └── Theme integration
│
├── GridModals.tsx (151 lines)
│   ├── CellViewer modal integration
│   ├── RowViewer modal integration
│   ├── Export modals management
│   └── Desensitization modal handling
│
└── ResultGridPaginationRefactored.tsx (501 lines)
    ├── Hook composition orchestration
    ├── Props transformation and distribution
    ├── Event coordination
    └── Backward compatibility layer
```

### Type System (`/types/`)
```
└── index.ts (191 lines)
    ├── ResultGridPaginationProps (67 properties)
    ├── Hook interface definitions
    ├── Service layer types
    └── AG-Grid integration types
```

## Integration Points

### Redux State Integration

**Primary State Dependencies:**
```typescript
// Query execution state
queryTabsSlice: {
  activeTabKey: string,
  tabInfoMap: Record<string, TabInfo>,
  executeActiveTabParams: Record<string, any>
}

// Result data state  
resultTabsSlice: {
  executeKeyListMap: Record<string, string[]>,
  resultTabMap: Record<string, ResultTab>,
  activeResultKeyMap: Record<string, string>
}

// User preferences
login.userInfo.copySetting: boolean,
appearance.theme: 'light' | 'dark'
```

### AG-Grid Deep Integration

**Infinite Scroll Configuration:**
```typescript
const infiniteModeOptions = {
  rowModelType: 'infinite',
  cacheBlockSize: rowNum,           // Dynamic block size
  cacheOverflowSize: 0,             // No overflow for memory optimization
  maxConcurrentDatasourceRequests: 1, // Prevent race conditions
  infiniteInitialRowCount: 1000,    // Initial estimate
  maxBlocksInCache: 5               // Memory management
};
```

**Data Source Integration:**
```typescript
const datasource: IDatasource = {
  getRows: async (params: IGetRowsParams) => {
    const { startRow, endRow, sortModel, filterModel } = params;
    
    // Integrate with N+1 pagination
    const pageSize = endRow - startRow;
    const result = await gridData.commonGetRows(startRow, pageSize, sortModel, filterModel);
    
    // Handle success/failure callbacks
    if (result.success) {
      params.successCallback(result.rowData, result.lastRow);
    } else {
      params.failCallback();
    }
  }
};
```

### API Service Integration

**Primary API Endpoints:**
```typescript
// Core data fetching
executeSqlSegment(params): Promise<SegmentResult>
├── Pagination parameters: startRow, pageSize
├── Filter/sort integration: sortModel, filterModel  
├── Connection context: connectionId, dataSourceType
└── Query context: statements, operatingObject

// Supporting services
explainSqlStatement(): Query execution plans
getTxModeOfConnection(): Transaction mode detection
queryConnectionsAndUserSettings(): Permission context
```

### Permission System Integration

**Permission-Based Feature Control:**
```typescript
interface PermissionResult {
  resultCellCopy: boolean;              // Cell copy permissions
  resultSetBinaryFileDownload: boolean; // Binary download permissions
  supportResultSetSqlGenerator: boolean; // SQL generation support
  resultSetSqlGenerator: boolean;       // SQL generation permissions
  allNodePathString: string[];          // Node access permissions
}

// Permission-based UI adaptation
const canCopy = permissionResult?.resultCellCopy && copySetting;
const canExport = permissionResult?.allNodePathString?.length > 0;
const canGenerateSQL = permissionResult?.supportResultSetSqlGenerator;
```

## Development Patterns

### Adding New Features

**1. Extend Hook Layer:**
```typescript
// Create new specialized hook
export function useNewFeature(props: UseNewFeatureProps): UseNewFeatureReturn {
  // Hook implementation
  return { /* feature API */ };
}

// Integrate in main component
const newFeature = useNewFeature({
  gridApiRef: gridConfig.gridApiRef,
  // ... other dependencies
});
```

**2. Extend Service Layer:**
```typescript
// Add service methods
export class NewFeatureService {
  static async performOperation(params: OperationParams): Promise<OperationResult> {
    // Service implementation
  }
}
```

**3. Integrate in Main Component:**
```typescript
// Compose new functionality
const mainComponent = (props) => {
  const newFeature = useNewFeature(newFeatureProps);
  // ... integrate with existing hooks
};
```

### Testing Strategies

**Hook Testing:**
```typescript
import { renderHook } from '@testing-library/react-hooks';

test('useGridData pagination', () => {
  const { result } = renderHook(() => useGridData(mockProps));
  expect(result.current.paginationData).toEqual(expectedPagination);
});
```

**Service Testing:**
```typescript
test('GridDataService fetchBlockData', async () => {
  const result = await GridDataService.fetchBlockDataOrFromStore(1, [], {}, mockParams);
  expect(result.success).toBe(true);
});
```

**Integration Testing:**
```typescript
test('Grid component integration', () => {
  render(<ResultGridPaginationRefactored {...mockProps} />);
  // Test user interactions and data flow
});
```

### Debugging Strategies

**Development Mode Debugging:**
```typescript
if (process.env.NODE_ENV === 'development') {
  console.log('N+1 Pagination Debug:', {
    pageSize,
    resultDataLength: resultData?.length,
    hasNext,
    calculatedTotal
  });
}
```

**Performance Monitoring:**
```typescript
// AG-Grid performance tracking
const onGridReady = (params) => {
  console.log('Grid ready performance:', {
    rowCount: params.api.getDisplayedRowCount(),
    memoryUsage: performance.memory?.usedJSHeapSize
  });
};
```

**Error Boundary Integration:**
```typescript
// Comprehensive error tracking
useEffect(() => {
  const errorHandler = (error) => {
    console.error('Grid error:', error);
    // Report to error tracking service
  };
  window.addEventListener('error', errorHandler);
  return () => window.removeEventListener('error', errorHandler);
}, []);
```

### Migration and Compatibility

**Backward Compatibility Layer:**
```typescript
// A/B testing support
const useRefactored = process.env.NODE_ENV === 'development' 
  ? true  // Use refactored version in development
  : false; // Use original version in production

return useRefactored ? 
  <ResultGridPaginationRefactored {...props} /> : 
  <ResultGridPagination {...props} />;
```

**Gradual Migration Strategy:**
1. **Phase 1**: A/B testing with feature flags
2. **Phase 2**: Gradual rollout to user segments
3. **Phase 3**: Full migration with original component removal

**API Compatibility:**
All existing props and callback interfaces are maintained for seamless migration without breaking changes.