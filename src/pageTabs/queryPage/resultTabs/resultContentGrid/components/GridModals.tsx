import React from 'react'
import type { GridA<PERSON> } from '@ag-grid-community/core'
import { isEmpty } from 'lodash'
import { CellViewerModal } from '../CellViewerModal'
import { RowViewerModal } from '../RowViewerModal'
import { AddSelectedResultExportModal } from '../AddSelectedResultExportModal'
import { AddResultExportModal } from '../AddResultExportModal'
import { ResultAllExport } from '../ResultAllExport'
import { DesensitizedFieldsModal } from '../DesensitizedFieldsModal'

interface GridModalsProps {
  // Grid API
  gridApi: GridApi | null
  
  // 焦点数据
  focusedRowIndex: number | null
  focusedRowData: any
  
  // 结果数据
  result: any
  detailedResultData: any[]
  
  // 权限和配置
  editable: boolean
  connectionType: string
  permissionResult?: any
  canCopyCell: boolean
  type?: string
  isExplain?: boolean
  
  // 弹窗状态
  visibleCellViewer: boolean
  visibleRowViewer: boolean
  visibleSelectedExport: boolean
  visibleCommonExport: boolean
  visibleExportAll: boolean
  visibleDesensitizedFields: boolean
  
  // 弹窗控制
  setVisibleCellViewer: React.Dispatch<React.SetStateAction<boolean>>
  setVisibleRowViewer: React.Dispatch<React.SetStateAction<boolean>>
  setVisibleSelectedExport: React.Dispatch<React.SetStateAction<boolean>>
  setVisibleCommonExport: React.Dispatch<React.SetStateAction<boolean>>
  setVisibleExportAll: React.Dispatch<React.SetStateAction<boolean>>
  setVisibleDesensitizedFields: React.Dispatch<React.SetStateAction<boolean>>
  
  // 数据操作
  updateFocusedCell: (params: any) => void
  downloadFocusedCell: (params: any) => void
  fetchFocusedCell: (newData: any, oldData: any) => Promise<any[]>
  
  // 导出操作
  handleExportAllResult: (data: any) => Promise<void>
  applyDataExportPermission: (exportType?: string) => Promise<void>
  
  // 行查看器数据
  rowViewerResultData: any[]
  curPagination: any
  rowNum: number
  setRowViewerResultData: React.Dispatch<React.SetStateAction<any[]>>
  
  // 行导航
  nextRowIndex: () => void
  lastRowIndex: () => void
  
  // 其他
  doubleCheckType?: 'NONE' | 'SMS' | 'OTP' | null | undefined
  filterNames?: string[]
}

export const GridModals: React.FC<GridModalsProps> = ({
  gridApi,
  focusedRowIndex,
  focusedRowData,
  result,
  detailedResultData,
  editable,
  connectionType,
  permissionResult,
  canCopyCell,
  type,
  isExplain,
  visibleCellViewer,
  visibleRowViewer,
  visibleSelectedExport,
  visibleCommonExport,
  visibleExportAll,
  visibleDesensitizedFields,
  setVisibleCellViewer,
  setVisibleRowViewer,
  setVisibleSelectedExport,
  setVisibleCommonExport,
  setVisibleExportAll,
  setVisibleDesensitizedFields,
  updateFocusedCell,
  downloadFocusedCell,
  fetchFocusedCell,
  handleExportAllResult,
  applyDataExportPermission,
  rowViewerResultData,
  curPagination,
  rowNum,
  setRowViewerResultData,
  nextRowIndex,
  lastRowIndex,
  doubleCheckType,
  filterNames
}) => {
  
  // 计算行查看器的绝对索引和数据
  const getRowViewerData = () => {
    if (!visibleRowViewer || focusedRowIndex === null) {
      return {
        currentRowData: {},
        isLastRow: true,
        absoluteRowIndex: 0
      }
    }
    
    // 计算绝对索引：第二页开始每次减一修正偏移
    const pageNumber = curPagination?.pageNumber || 1
    const pageSize = curPagination?.pageSize || rowNum
    const offset = pageNumber > 1 ? pageNumber - 1 : 0
    const absoluteRowIndex = (pageNumber - 1) * pageSize + focusedRowIndex + offset
    const currentRowData = rowViewerResultData[absoluteRowIndex] || {}
    const nextAbsoluteIndex = absoluteRowIndex + 1
    const isLastRow = nextAbsoluteIndex >= rowViewerResultData.length
    
    return {
      currentRowData,
      isLastRow,
      absoluteRowIndex
    }
  }

  const { currentRowData, isLastRow } = getRowViewerData()

  return (
    <>
      {/* 单元格查看器弹窗 */}
      <CellViewerModal
        gridApi={gridApi}
        rowIndex={focusedRowIndex}
        rowData={focusedRowData}
        initRowData={isEmpty(focusedRowData?._initData) ? focusedRowData : focusedRowData?._initData}
        resultData={detailedResultData}
        //db2 不支持编辑，后端暂无相关处理逻辑 前端处理下
        editable={editable && connectionType !== 'DB2'}
        allowResultCopy={permissionResult?.resultCellCopy}
        visible={visibleCellViewer}
        allowBinaryCellDownload={permissionResult?.resultSetBinaryFileDownload}
        setVisible={setVisibleCellViewer}
        updateFocusedCell={updateFocusedCell}
        downloadFocusedCell={downloadFocusedCell}
        fetchFocusedCell={fetchFocusedCell}
        type={type!}
        isExplain={isExplain || false}
      />

      {/* 行查看器弹窗 */}
      {visibleRowViewer && (
        <RowViewerModal
          gridApi={gridApi}
          rowData={currentRowData}
          resultData={detailedResultData}
          //db2 不支持编辑，后端暂无相关处理逻辑 前端处理下
          editable={editable && connectionType !== 'DB2'}
          visible={visibleRowViewer}
          setVisible={setVisibleRowViewer}
          updateFocusedCell={updateFocusedCell}
          downloadFocusedCell={downloadFocusedCell}
          fetchFocusedCell={fetchFocusedCell}
          rowIndex={focusedRowIndex}
          nextRowIndex={nextRowIndex}
          lastRowIndex={lastRowIndex}
          isLastRowIndex={isLastRow}
          setRowViewerResultData={() => {
            setRowViewerResultData([])
          }}
          resultAllowCopy={canCopyCell}
        />
      )}

      {/* 选中结果导出弹窗 */}
      <AddSelectedResultExportModal
        result={result}
        visible={visibleSelectedExport}
        setVisible={setVisibleSelectedExport}
        gridApi={gridApi}
        permissionResult={permissionResult}
      />

      {/* 通用结果导出弹窗 */}
      <AddResultExportModal
        result={result}
        visible={visibleCommonExport}
        setVisible={setVisibleCommonExport}
        applyDataExportPermission={applyDataExportPermission}
        permissionResult={permissionResult}
      />

      {/* 全量结果导出弹窗 */}
      <ResultAllExport
        result={result}
        visible={visibleExportAll}
        setVisible={setVisibleExportAll}
        hanldeExportAll={handleExportAllResult}
        applyDataExportPermission={applyDataExportPermission}
        permissionResult={permissionResult}
      />

      {/* 脱敏字段弹窗 */}
      <DesensitizedFieldsModal
        result={result}
        visible={visibleDesensitizedFields}
        setVisible={setVisibleDesensitizedFields}
        doubleCheckType={doubleCheckType}
        filterNames={filterNames || []}
      />
    </>
  )
}