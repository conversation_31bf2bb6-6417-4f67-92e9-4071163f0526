import React, { useRef, useCallback, useEffect } from 'react'
import { AgGridReact } from '@ag-grid-community/react'
import classNames from 'classnames'
import styles from '../grid.module.scss'
import type { 
  GridReadyEvent, 
  CellFocusedEvent, 
  CellKeyDownEvent, 
  CellClickedEvent,
  CellContextMenuEvent,
  RowEditingStartedEvent,
  RowEditingStoppedEvent,
  SelectionChangedEvent,
  SortChangedEvent,
  FilterChangedEvent,
  FirstDataRenderedEvent
} from '@ag-grid-community/core'
import type { IGridContext } from 'src/components'

interface GridContainerProps {
  // Grid 配置
  gridConfig: any
  infiniteModeOptions: any
  columnDefs: any[]
  defaultColDef: any
  datasource: any
  
  // 样式和主题
  theme: string
  canCopy: boolean
  tabResultKey: string
  aggridKey: number
  
  // 分页配置
  curPagination: any
  rowNum: number
  
  // 上下文
  agContext: IGridContext
  
  // 事件处理器
  onGridReady: (event: GridReadyEvent) => void
  onCellFocused: (event: CellFocusedEvent) => void
  onCellKeyDown: (event: CellKeyDownEvent) => void
  onCellClicked: (params: CellClickedEvent) => void
  onCellContextMenu: (params: CellContextMenuEvent) => void
  onRowEditingStarted: (event: RowEditingStartedEvent) => void
  onRowEditingStopped: (event: RowEditingStoppedEvent) => void
  onSelectionChanged: (event: SelectionChangedEvent) => void
  onSortChanged: () => void
  onFilterChanged: () => void
  onFirstDataRendered?: (params: FirstDataRenderedEvent) => void
}

export const GridContainer: React.FC<GridContainerProps> = ({
  gridConfig,
  infiniteModeOptions,
  columnDefs,
  defaultColDef,
  datasource,
  theme,
  canCopy,
  tabResultKey,
  aggridKey,
  curPagination,
  rowNum,
  agContext,
  onGridReady,
  onCellFocused,
  onCellKeyDown,
  onCellClicked,
  onCellContextMenu,
  onRowEditingStarted,
  onRowEditingStopped,
  onSelectionChanged,
  onSortChanged,
  onFilterChanged,
  onFirstDataRendered
}) => {
  
  const gridContainerRef = useRef<HTMLDivElement>(null)

  // 处理单元格点击，避免在编辑模式下触发自定义处理器
  const handleCellClicked = useCallback((params: CellClickedEvent) => {
    // 检查是否有单元格正在编辑
    const editingCells = params.api?.getEditingCells()
    const isEditing = editingCells && editingCells.length > 0

    // 如果正在编辑模式，不调用自定义处理器
    if (!isEditing) {
      onCellClicked(params)
    }
  }, [onCellClicked])

  // 处理右键菜单显示前的选区逻辑
  const handleCellContextMenu = useCallback((params: CellContextMenuEvent) => {
    onCellContextMenu(params)
  }, [onCellContextMenu])

  return (
    <div
      id={`${tabResultKey}gridWrapper`}
      ref={gridContainerRef}
      className={classNames(
        styles.gridWrapper,
        theme === 'dark' ? 'ag-theme-balham-dark' : 'ag-theme-balham',
        !canCopy && styles.unCopyable,
      )}
    >
      <AgGridReact
        {...gridConfig}
        {...infiniteModeOptions}
        columnDefs={columnDefs}
        cacheBlockSize={rowNum}
        defaultColDef={defaultColDef}
        datasource={datasource}
        onGridReady={onGridReady}
        onCellFocused={onCellFocused}
        onCellKeyDown={onCellKeyDown}
        onCellClicked={handleCellClicked}
        onCellContextMenu={handleCellContextMenu}
        onRowEditingStarted={onRowEditingStarted}
        onRowEditingStopped={onRowEditingStopped}
        context={agContext}
        suppressDragLeaveHidesColumns={true}
        // 添加 key 属性，需要刷新的地方用递增的数值setAggridKey
        key={aggridKey}
        onSelectionChanged={onSelectionChanged}
        onSortChanged={onSortChanged}
        pagination={true}
        paginationPageSize={curPagination?.pageSize || rowNum}
        suppressPaginationPanel={true}
        onFilterChanged={onFilterChanged}
        // 首次数据渲染完成回调
        onFirstDataRendered={onFirstDataRendered}
      />
    </div>
  )
}